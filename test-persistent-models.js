const fs = require('fs');
const path = require('path');

console.log('🔧 Testing Persistent Model Configuration');
console.log('=' .repeat(60));

// Test 1: Check current custom models
console.log('\n📋 Test 1: Current Custom Models');
const customModelsPath = path.join(__dirname, 'src', 'config', 'models.json');
console.log(`   Path: ${customModelsPath}`);

if (fs.existsSync(customModelsPath)) {
    try {
        const customModels = JSON.parse(fs.readFileSync(customModelsPath, 'utf8'));
        console.log(`   ✅ Found ${customModels.length} custom models:`);
        customModels.forEach((model, index) => {
            console.log(`      ${index + 1}. ${model.name} (${model.id})`);
        });
    } catch (error) {
        console.log(`   ❌ Error reading custom models: ${error.message}`);
    }
} else {
    console.log('   ⚠️  No custom models file found');
}

// Test 2: Simulate config loading
console.log('\n📋 Test 2: Simulating Config Loading');

// Test desktop config
console.log('   Testing desktop config...');
try {
    // Simulate the loadModels function from desktop.js
    function loadModelsDesktop() {
        const customModelsPath = path.join(__dirname, 'src', 'config', 'models.json');
        
        if (fs.existsSync(customModelsPath)) {
            try {
                const customModels = JSON.parse(fs.readFileSync(customModelsPath, 'utf8'));
                if (customModels.length > 0) {
                    console.log(`      ✅ Desktop: Loaded ${customModels.length} custom models`);
                    return customModels.map(model => model.id);
                }
            } catch (error) {
                console.log(`      ❌ Desktop: Error loading custom models: ${error.message}`);
            }
        }
        
        console.log('      ⚠️  Desktop: No custom models, using defaults');
        return ['google/gemini-2.0-flash-exp:free', 'google/gemma-3-27b-it:free'];
    }
    
    const desktopModels = loadModelsDesktop();
    console.log(`      Desktop models result: ${desktopModels.length} models`);
    
} catch (error) {
    console.log(`   ❌ Desktop config test failed: ${error.message}`);
}

// Test main config
console.log('   Testing main config...');
try {
    // Simulate the loadModels function from config.js
    function loadModelsMain() {
        const customModelsPath = path.join(__dirname, 'src', 'config', 'models.json');
        
        if (fs.existsSync(customModelsPath)) {
            try {
                const customModels = JSON.parse(fs.readFileSync(customModelsPath, 'utf8'));
                if (customModels.length > 0) {
                    console.log(`      ✅ Main: Loaded ${customModels.length} custom models`);
                    return customModels.map(model => model.id);
                }
            } catch (error) {
                console.log(`      ❌ Main: Error loading custom models: ${error.message}`);
            }
        }
        
        console.log('      ⚠️  Main: No custom models, using defaults');
        return ['google/gemini-2.0-flash-exp:free', 'google/gemma-3-27b-it:free'];
    }
    
    const mainModels = loadModelsMain();
    console.log(`      Main models result: ${mainModels.length} models`);
    
} catch (error) {
    console.log(`   ❌ Main config test failed: ${error.message}`);
}

// Test 3: Simulate removing all models
console.log('\n📋 Test 3: Simulating Model Removal');

// Create backup of current models
let originalModels = null;
if (fs.existsSync(customModelsPath)) {
    originalModels = fs.readFileSync(customModelsPath, 'utf8');
    console.log('   📁 Backed up current models');
}

// Test empty models file
console.log('   🗑️  Testing with empty models file...');
fs.writeFileSync(customModelsPath, '[]', 'utf8');

// Test config loading with empty models
function testWithEmptyModels() {
    const customModels = JSON.parse(fs.readFileSync(customModelsPath, 'utf8'));
    if (customModels.length === 0) {
        console.log('      ✅ Empty models detected, should use defaults');
        return ['google/gemini-2.0-flash-exp:free', 'google/gemma-3-27b-it:free'];
    } else {
        console.log(`      ⚠️  Expected empty, but found ${customModels.length} models`);
        return customModels.map(model => model.id);
    }
}

const emptyTestResult = testWithEmptyModels();
console.log(`      Result: ${emptyTestResult.length} models (should be defaults)`);

// Test 4: Simulate app restart
console.log('\n📋 Test 4: Simulating App Restart');

// Restore original models
if (originalModels) {
    fs.writeFileSync(customModelsPath, originalModels, 'utf8');
    console.log('   🔄 Restored original models');
    
    // Test loading after restart
    const restoredModels = JSON.parse(fs.readFileSync(customModelsPath, 'utf8'));
    console.log(`   ✅ After restart: ${restoredModels.length} models restored`);
    restoredModels.forEach((model, index) => {
        console.log(`      ${index + 1}. ${model.name}`);
    });
} else {
    console.log('   ⚠️  No original models to restore');
}

// Test 5: Expected behavior summary
console.log('\n🎯 Expected Behavior After Fix:');
console.log('   1. ✅ App loads ONLY your custom models from models.json');
console.log('   2. ✅ When you remove all models → models.json becomes empty');
console.log('   3. ✅ When models.json is empty → app uses default models as fallback');
console.log('   4. ✅ When you restart → app respects the current state of models.json');
console.log('   5. ✅ No more hardcoded models overriding your choices');

console.log('\n🚀 Next Steps:');
console.log('   1. Restart your application: npm start');
console.log('   2. Try removing your DeepSeek model');
console.log('   3. Close and restart the app');
console.log('   4. The model should stay removed (not come back)');
console.log('   5. Add models back using the "Add Model" button');
