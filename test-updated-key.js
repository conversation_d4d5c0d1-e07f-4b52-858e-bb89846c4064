const axios = require('axios');
const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

// Load environment variables
require('dotenv').config();

console.log('🔑 Testing Updated API Key');
console.log('=' .repeat(50));

const apiKey = process.env.API_KEY;

if (!apiKey) {
    console.log('❌ No API key found in .env file');
    console.log('💡 Make sure you have API_KEY=your_key in your .env file');
    process.exit(1);
}

console.log('📋 API Key Info:');
console.log(`   Masked: ${apiKey.substring(0, 15)}...${apiKey.substring(apiKey.length - 4)}`);
console.log(`   Length: ${apiKey.length}`);
console.log(`   Format: ${apiKey.startsWith('sk-or-v1-') && apiKey.length === 73 ? 'Valid' : 'Invalid'}`);

if (!apiKey.startsWith('sk-or-v1-') || apiKey.length !== 73) {
    console.log('❌ Invalid API key format');
    console.log('💡 OpenRouter API keys should start with "sk-or-v1-" and be 73 characters long');
    process.exit(1);
}

console.log('\n🧪 Testing API Key with OpenRouter...');

axios.post('https://openrouter.ai/api/v1/chat/completions', {
    model: 'google/gemini-2.0-flash-exp:free',
    messages: [{ role: 'user', content: 'Hello! Please respond with "API key is working"' }],
    max_tokens: 10
}, {
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'https://telegram-mcq-tf-bot.com',
        'X-Title': 'Telegram MCQ/TF Question Generator'
    },
    timeout: 15000
}).then(response => {
    console.log('✅ API Key Test: SUCCESS!');
    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${response.data.choices[0].message.content}`);
    
    console.log('\n🔧 Updating database with new API key...');
    
    // Update database
    const dbPath = path.join(__dirname, 'data', 'bot.db');
    
    if (fs.existsSync(dbPath)) {
        const db = new sqlite3.Database(dbPath, (err) => {
            if (err) {
                console.error('❌ Database connection error:', err.message);
                return;
            }
            
            // Clear old keys and add new one
            db.run('DELETE FROM api_keys', (err) => {
                if (err) {
                    console.log('⚠️  Could not clear old keys:', err.message);
                }
                
                db.run('INSERT INTO api_keys (key, is_active, last_used, created_at) VALUES (?, 1, ?, ?)', 
                    [apiKey, Date.now(), Date.now()], function(err) {
                        if (err) {
                            console.log('❌ Could not add key to database:', err.message);
                        } else {
                            console.log(`✅ Updated database with new API key (ID: ${this.lastID})`);
                        }
                        
                        db.close();
                        
                        console.log('\n🎉 SUCCESS! Your API key is working');
                        console.log('✅ Restart the application: npm start');
                        console.log('✅ The 401 error should now be resolved');
                    });
            });
        });
    } else {
        console.log('⚠️  Database not found, but API key is working');
        console.log('✅ The app will create the database when you start it');
    }
    
}).catch(error => {
    console.log('❌ API Key Test: FAILED');
    console.log(`   Error: ${error.message}`);
    
    if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
        
        if (error.response.status === 401) {
            console.log('\n💡 This API key is still not working. Try these steps:');
            console.log('   1. Go to https://openrouter.ai/settings/keys');
            console.log('   2. Delete the old key and create a brand new one');
            console.log('   3. Make sure your account is verified');
            console.log('   4. Check if you have available credits');
            console.log('   5. Copy the new key exactly (all 73 characters)');
            console.log('   6. Update your .env file with the new key');
        }
    }
});
