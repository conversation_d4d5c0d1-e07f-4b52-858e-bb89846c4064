const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// Load environment variables
require('dotenv').config();

console.log('🔍 API Key Diagnostic Tool');
console.log('=' .repeat(50));

// Check environment API key
const envApiKey = process.env.API_KEY;
console.log('\n📋 Environment API Key:');
console.log(`   Exists: ${!!envApiKey}`);
if (envApiKey) {
    console.log(`   Length: ${envApiKey.length}`);
    console.log(`   Format: ${envApiKey.startsWith('sk-or-v1-') ? 'Valid' : 'Invalid'}`);
    console.log(`   Masked: ${envApiKey.substring(0, 15)}...${envApiKey.substring(envApiKey.length - 4)}`);
}

// Check database
const dbPath = path.join(__dirname, 'data', 'bot.db');
console.log(`\n📋 Database Path: ${dbPath}`);
console.log(`   Exists: ${fs.existsSync(dbPath)}`);

if (fs.existsSync(dbPath)) {
    const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
            console.error('❌ Database connection error:', err.message);
            return;
        }
        
        console.log('\n📋 Database API Keys:');
        
        // Check if api_keys table exists
        db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys'", [], (err, row) => {
            if (err) {
                console.error('❌ Error checking table:', err.message);
                db.close();
                return;
            }
            
            if (!row) {
                console.log('   Table: Does not exist');
                console.log('\n🔧 Creating api_keys table...');
                
                db.run(`CREATE TABLE api_keys (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT NOT NULL,
                    is_active INTEGER DEFAULT 1,
                    last_used INTEGER,
                    created_at INTEGER
                )`, (err) => {
                    if (err) {
                        console.error('❌ Error creating table:', err.message);
                    } else {
                        console.log('✅ Table created successfully');
                        addApiKeyToDatabase();
                    }
                });
            } else {
                console.log('   Table: Exists');
                
                // Check existing keys
                db.all('SELECT id, key, is_active, last_used FROM api_keys', [], (err, rows) => {
                    if (err) {
                        console.error('❌ Error fetching keys:', err.message);
                        db.close();
                        return;
                    }
                    
                    console.log(`   Count: ${rows.length}`);
                    
                    if (rows.length > 0) {
                        rows.forEach((row, index) => {
                            const maskedKey = row.key.substring(0, 15) + '...' + row.key.substring(row.key.length - 4);
                            console.log(`   Key ${index + 1}: ${maskedKey} (Active: ${row.is_active})`);
                        });
                        
                        // Check if any active keys exist
                        const activeKeys = rows.filter(row => row.is_active === 1);
                        if (activeKeys.length === 0) {
                            console.log('\n⚠️  No active API keys found in database');
                            addApiKeyToDatabase();
                        } else {
                            console.log(`\n✅ Found ${activeKeys.length} active API key(s)`);
                            testApiKey(activeKeys[0].key);
                        }
                    } else {
                        console.log('   Keys: None found');
                        addApiKeyToDatabase();
                    }
                });
            }
        });
        
        function addApiKeyToDatabase() {
            if (!envApiKey) {
                console.log('❌ No environment API key to add');
                db.close();
                return;
            }
            
            console.log('\n🔧 Adding environment API key to database...');
            
            // First deactivate all existing keys
            db.run('UPDATE api_keys SET is_active = 0', (err) => {
                if (err) {
                    console.error('❌ Error deactivating keys:', err.message);
                    db.close();
                    return;
                }
                
                // Add the new key
                db.run('INSERT INTO api_keys (key, is_active, last_used, created_at) VALUES (?, 1, ?, ?)', 
                    [envApiKey, Date.now(), Date.now()], function(err) {
                        if (err) {
                            console.error('❌ Error adding API key:', err.message);
                        } else {
                            console.log(`✅ API key added with ID ${this.lastID}`);
                            testApiKey(envApiKey);
                        }
                    });
            });
        }
        
        function testApiKey(apiKey) {
            console.log('\n🧪 Testing API Key...');
            
            const axios = require('axios');
            
            axios.post('https://openrouter.ai/api/v1/chat/completions', {
                model: 'google/gemini-2.0-flash-exp:free',
                messages: [{ role: 'user', content: 'Hi' }],
                max_tokens: 5
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`,
                    'HTTP-Referer': 'https://telegram-mcq-tf-bot.com',
                    'X-Title': 'Telegram MCQ/TF Question Generator'
                },
                timeout: 10000
            }).then(response => {
                console.log('✅ API Key Test: SUCCESS');
                console.log(`   Status: ${response.status}`);
                console.log('🎉 API key is working correctly!');
                db.close();
            }).catch(error => {
                console.log('❌ API Key Test: FAILED');
                console.log(`   Error: ${error.message}`);
                if (error.response) {
                    console.log(`   Status: ${error.response.status}`);
                    console.log(`   Data: ${JSON.stringify(error.response.data)}`);
                }
                
                console.log('\n💡 Possible solutions:');
                console.log('   1. Check if your API key is valid');
                console.log('   2. Verify your OpenRouter account has credits');
                console.log('   3. Make sure the API key has the correct permissions');
                console.log('   4. Try generating a new API key from OpenRouter');
                
                db.close();
            });
        }
    });
} else {
    console.log('❌ Database file not found');
}
