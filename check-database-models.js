const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

console.log('🔍 Checking Database for Hardcoded Models');
console.log('=' .repeat(60));

const dbPath = path.join(__dirname, 'data', 'bot.db');

if (!fs.existsSync(dbPath)) {
    console.log('❌ Database file not found');
    process.exit(1);
}

const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ Database connection error:', err.message);
        process.exit(1);
    }
    
    console.log('✅ Connected to database');
    
    // Check all tables
    db.all("SELECT name FROM sqlite_master WHERE type='table'", [], (err, tables) => {
        if (err) {
            console.error('❌ Error fetching tables:', err.message);
            db.close();
            return;
        }
        
        console.log('\n📋 Database Tables:');
        tables.forEach((table, index) => {
            console.log(`   ${index + 1}. ${table.name}`);
        });
        
        // Check each table for model-related data
        checkTablesForModels(tables);
    });
    
    function checkTablesForModels(tables) {
        console.log('\n🔍 Checking Tables for Model Data:');
        
        let tablesChecked = 0;
        const totalTables = tables.length;
        
        tables.forEach(table => {
            const tableName = table.name;
            
            // Get table schema
            db.all(`PRAGMA table_info(${tableName})`, [], (err, columns) => {
                if (err) {
                    console.error(`❌ Error getting schema for ${tableName}:`, err.message);
                    tablesChecked++;
                    checkComplete();
                    return;
                }
                
                // Check if table has model-related columns
                const modelColumns = columns.filter(col => 
                    col.name.toLowerCase().includes('model') || 
                    col.name.toLowerCase().includes('api') ||
                    col.name.toLowerCase().includes('key')
                );
                
                if (modelColumns.length > 0) {
                    console.log(`\n📋 Table: ${tableName}`);
                    console.log(`   Model-related columns: ${modelColumns.map(c => c.name).join(', ')}`);
                    
                    // Check for actual data
                    db.all(`SELECT * FROM ${tableName} LIMIT 10`, [], (err, rows) => {
                        if (err) {
                            console.error(`❌ Error querying ${tableName}:`, err.message);
                        } else {
                            console.log(`   Rows: ${rows.length}`);
                            
                            if (rows.length > 0) {
                                // Check for hardcoded model names in the data
                                const modelPatterns = [
                                    'google/gemini', 'google/gemma', 'nvidia/llama', 
                                    'deepseek/', 'qwen/', 'meta-llama/', 'mistralai/'
                                ];
                                
                                rows.forEach((row, index) => {
                                    Object.keys(row).forEach(key => {
                                        const value = String(row[key] || '');
                                        modelPatterns.forEach(pattern => {
                                            if (value.includes(pattern)) {
                                                console.log(`   ⚠️  Row ${index + 1}, ${key}: Contains "${pattern}"`);
                                                console.log(`      Value: ${value}`);
                                            }
                                        });
                                    });
                                });
                            }
                        }
                        
                        tablesChecked++;
                        checkComplete();
                    });
                } else {
                    tablesChecked++;
                    checkComplete();
                }
            });
        });
        
        function checkComplete() {
            if (tablesChecked === totalTables) {
                console.log('\n🎯 Database Check Complete');
                db.close();
            }
        }
    }
});
