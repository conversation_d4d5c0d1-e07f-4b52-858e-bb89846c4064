const axios = require('axios');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

console.log('🔑 API Key Tester');
console.log('=' .repeat(50));
console.log('This tool will help you test a new OpenRouter API key');
console.log('');
console.log('📋 Instructions:');
console.log('1. Go to https://openrouter.ai/');
console.log('2. Sign in or create an account');
console.log('3. Go to Settings → API Keys');
console.log('4. Create a new API key');
console.log('5. Copy the key and paste it below');
console.log('');

rl.question('🔑 Enter your new OpenRouter API key (starts with sk-or-v1-): ', (newApiKey) => {
    if (!newApiKey || !newApiKey.trim()) {
        console.log('❌ No API key provided');
        rl.close();
        return;
    }
    
    newApiKey = newApiKey.trim();
    
    // Validate format
    if (!newApiKey.startsWith('sk-or-v1-')) {
        console.log('❌ Invalid API key format. Must start with "sk-or-v1-"');
        rl.close();
        return;
    }
    
    if (newApiKey.length !== 73) {
        console.log(`❌ Invalid API key length. Expected 73 characters, got ${newApiKey.length}`);
        rl.close();
        return;
    }
    
    console.log('✅ API key format looks correct');
    console.log(`   Length: ${newApiKey.length}`);
    console.log(`   Masked: ${newApiKey.substring(0, 15)}...${newApiKey.substring(newApiKey.length - 4)}`);
    
    console.log('\n🧪 Testing API key...');
    
    // Test the API key
    axios.post('https://openrouter.ai/api/v1/chat/completions', {
        model: 'google/gemini-2.0-flash-exp:free',
        messages: [{ role: 'user', content: 'Hello, please respond with just "API key works"' }],
        max_tokens: 10
    }, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${newApiKey}`,
            'HTTP-Referer': 'https://telegram-mcq-tf-bot.com',
            'X-Title': 'Telegram MCQ/TF Question Generator'
        },
        timeout: 15000
    }).then(response => {
        console.log('✅ API Key Test: SUCCESS!');
        console.log(`   Status: ${response.status}`);
        console.log(`   Response: ${response.data.choices[0].message.content}`);
        
        console.log('\n🔧 Updating your application...');
        
        // Update .env file
        const fs = require('fs');
        const path = require('path');
        
        const envPath = path.join(__dirname, '.env');
        let envContent = '';
        
        try {
            if (fs.existsSync(envPath)) {
                envContent = fs.readFileSync(envPath, 'utf8');
            }
        } catch (error) {
            console.log('⚠️  Could not read .env file, creating new one');
        }
        
        // Update or add the API_KEY line
        const lines = envContent.split('\n');
        let keyUpdated = false;
        
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].startsWith('API_KEY=')) {
                lines[i] = `API_KEY=${newApiKey}`;
                keyUpdated = true;
                break;
            }
        }
        
        if (!keyUpdated) {
            lines.push(`API_KEY=${newApiKey}`);
        }
        
        // Write back to .env file
        fs.writeFileSync(envPath, lines.join('\n'), 'utf8');
        console.log('✅ Updated .env file');
        
        // Update database
        const sqlite3 = require('sqlite3').verbose();
        const dbPath = path.join(__dirname, 'data', 'bot.db');
        
        if (fs.existsSync(dbPath)) {
            const db = new sqlite3.Database(dbPath, (err) => {
                if (err) {
                    console.error('❌ Database connection error:', err.message);
                    rl.close();
                    return;
                }
                
                // Deactivate all existing keys
                db.run('UPDATE api_keys SET is_active = 0', (err) => {
                    if (err) {
                        console.log('⚠️  Could not deactivate old keys:', err.message);
                    }
                    
                    // Add the new key
                    db.run('INSERT INTO api_keys (key, is_active, last_used, created_at) VALUES (?, 1, ?, ?)', 
                        [newApiKey, Date.now(), Date.now()], function(err) {
                            if (err) {
                                console.log('⚠️  Could not add key to database:', err.message);
                            } else {
                                console.log(`✅ Updated database with new API key (ID: ${this.lastID})`);
                            }
                            
                            db.close();
                            
                            console.log('\n🎉 SUCCESS! Your API key has been updated');
                            console.log('✅ You can now start the application with: npm start');
                            console.log('✅ The 401 error should be resolved');
                            
                            rl.close();
                        });
                });
            });
        } else {
            console.log('⚠️  Database not found, but .env file updated');
            console.log('✅ Start the application and it will create the database automatically');
            rl.close();
        }
        
    }).catch(error => {
        console.log('❌ API Key Test: FAILED');
        console.log(`   Error: ${error.message}`);
        
        if (error.response) {
            console.log(`   Status: ${error.response.status}`);
            console.log(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
            
            if (error.response.status === 401) {
                console.log('\n💡 This API key is not working. Possible issues:');
                console.log('   1. The API key is invalid or expired');
                console.log('   2. Your OpenRouter account needs verification');
                console.log('   3. You need to add credits to your account');
                console.log('   4. The API key doesn\'t have the right permissions');
                console.log('\n🔧 Try these steps:');
                console.log('   1. Go to https://openrouter.ai/settings/keys');
                console.log('   2. Delete the old key and create a new one');
                console.log('   3. Make sure your account is verified');
                console.log('   4. Check if you have available credits');
            }
        }
        
        rl.close();
    });
});
