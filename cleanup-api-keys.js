const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// Load environment variables
require('dotenv').config();

console.log('🧹 API Key Cleanup Tool');
console.log('=' .repeat(50));
console.log('This will ensure only ONE active API key exists in the database');

const envApiKey = process.env.API_KEY;
console.log('\n📋 Current .env API Key:');
console.log(`   Exists: ${!!envApiKey}`);
if (envApiKey) {
    console.log(`   Masked: ${envApiKey.substring(0, 15)}...${envApiKey.substring(envApiKey.length - 4)}`);
}

// Connect to database
const dbPath = path.join(__dirname, 'data', 'bot.db');

if (!fs.existsSync(dbPath)) {
    console.log('❌ Database file not found');
    process.exit(1);
}

const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ Database connection error:', err.message);
        process.exit(1);
    }
    
    console.log('✅ Connected to database');
    
    // Check current API keys
    console.log('\n📋 Current API Keys in Database:');
    db.all('SELECT id, key, is_active, last_used, created_at FROM api_keys ORDER BY created_at DESC', [], (err, rows) => {
        if (err) {
            console.error('❌ Error fetching keys:', err.message);
            db.close();
            return;
        }
        
        console.log(`   Total Keys: ${rows.length}`);
        
        if (rows.length === 0) {
            console.log('   No keys found in database');
            
            if (envApiKey) {
                console.log('\n🔧 Adding .env API key to database...');
                addEnvKeyToDatabase();
            } else {
                console.log('❌ No API key in .env file either');
                db.close();
            }
            return;
        }
        
        // Show all keys
        rows.forEach((row, index) => {
            const maskedKey = row.key.substring(0, 15) + '...' + row.key.substring(row.key.length - 4);
            const isActive = row.is_active === 1 ? 'ACTIVE' : 'inactive';
            const date = new Date(row.created_at).toLocaleString();
            console.log(`   ${index + 1}. ${maskedKey} (${isActive}) - Created: ${date}`);
        });
        
        // Find the key that matches .env
        const envKeyInDb = rows.find(row => row.key === envApiKey);
        
        if (envKeyInDb) {
            console.log('\n✅ .env API key found in database');
            
            // Clean up: keep only the .env key, remove all others
            console.log('🧹 Cleaning up duplicate keys...');
            
            // Delete all keys except the one matching .env
            db.run('DELETE FROM api_keys WHERE key != ?', [envApiKey], function(err) {
                if (err) {
                    console.error('❌ Error deleting duplicate keys:', err.message);
                    db.close();
                    return;
                }
                
                console.log(`✅ Removed ${this.changes} duplicate keys`);
                
                // Ensure the remaining key is active
                db.run('UPDATE api_keys SET is_active = 1, last_used = ? WHERE key = ?', 
                    [Date.now(), envApiKey], (err) => {
                        if (err) {
                            console.error('❌ Error activating key:', err.message);
                        } else {
                            console.log('✅ Activated the .env API key');
                        }
                        
                        showFinalStatus();
                    });
            });
        } else {
            console.log('\n⚠️  .env API key not found in database');
            
            if (envApiKey) {
                console.log('🔧 Replacing all database keys with .env key...');
                
                // Delete all existing keys
                db.run('DELETE FROM api_keys', (err) => {
                    if (err) {
                        console.error('❌ Error clearing keys:', err.message);
                        db.close();
                        return;
                    }
                    
                    console.log('✅ Cleared all existing keys');
                    addEnvKeyToDatabase();
                });
            } else {
                console.log('❌ No valid API key in .env file');
                db.close();
            }
        }
    });
    
    function addEnvKeyToDatabase() {
        db.run('INSERT INTO api_keys (key, is_active, last_used, created_at) VALUES (?, 1, ?, ?)', 
            [envApiKey, Date.now(), Date.now()], function(err) {
                if (err) {
                    console.error('❌ Error adding .env key to database:', err.message);
                } else {
                    console.log(`✅ Added .env API key to database (ID: ${this.lastID})`);
                }
                
                showFinalStatus();
            });
    }
    
    function showFinalStatus() {
        console.log('\n📋 Final Database Status:');
        db.all('SELECT id, key, is_active FROM api_keys', [], (err, rows) => {
            if (err) {
                console.error('❌ Error checking final status:', err.message);
            } else {
                console.log(`   Total Keys: ${rows.length}`);
                rows.forEach((row, index) => {
                    const maskedKey = row.key.substring(0, 15) + '...' + row.key.substring(row.key.length - 4);
                    const isActive = row.is_active === 1 ? 'ACTIVE' : 'inactive';
                    console.log(`   ${index + 1}. ${maskedKey} (${isActive})`);
                });
            }
            
            console.log('\n🎉 CLEANUP COMPLETE!');
            console.log('✅ Database now has only ONE API key');
            console.log('✅ Database and .env file are synchronized');
            console.log('✅ Future updates through the app will maintain this sync');
            console.log('\n🚀 Restart your application: npm start');
            
            db.close();
        });
    }
});
