/* CSS Variables for Theme System */
:root {
    /* Light Theme Colors */
    --bg-primary: #f5f7fa;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f8f9fa;
    --bg-accent: #f0f4ff;
    --bg-hover: #f8f9ff;

    --text-primary: #333333;
    --text-secondary: #666666;
    --text-muted: #999999;
    --text-inverse: #ffffff;

    --border-color: #e9ecef;
    --border-hover: #667eea;
    --border-focus: #667eea;

    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.1);
    --shadow-heavy: 0 8px 25px rgba(102, 126, 234, 0.15);

    --success-color: #28a745;
    --error-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --hover-transform: translateY(-1px);

    --success-bg: #d1f2d1;
    --error-bg: #f8d7da;
    --warning-bg: #fff3cd;
    --info-bg: #d1ecf1;
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --bg-accent: #2a2a3a;
    --bg-hover: #3a3a4a;

    --text-primary: #e0e0e0;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;
    --text-inverse: #1a1a1a;

    --border-color: #404040;
    --border-hover: #667eea;
    --border-focus: #667eea;

    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

    --shadow-light: 0 2px 10px rgba(0,0,0,0.3);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.3);
    --shadow-heavy: 0 8px 25px rgba(102, 126, 234, 0.25);

    --success-color: #4caf50;
    --error-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #2196f3;

    --success-bg: #1b4332;
    --error-bg: #5c1a1a;
    --warning-bg: #5c3317;
    --info-bg: #1a365d;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow: hidden;
    transition: background-color 0.3s ease, color 0.3s ease;
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo i {
    font-size: 1.5rem;
}

.logo h1 {
    font-size: 1.2rem;
    font-weight: 300;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

/* Button Styles */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-icon {
    padding: 0.5rem;
    background: rgba(255,255,255,0.1);
    color: white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Main Content */
.main-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
}

.screen.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Welcome Screen */
.welcome-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

.welcome-header {
    margin-bottom: 3rem;
}

.welcome-icon {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.welcome-header h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.welcome-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.question-type-selection {
    margin-bottom: 3rem;
}

.question-type-selection h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.type-buttons {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.type-btn {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    width: 250px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
}

.type-btn:hover {
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.type-btn.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.type-btn i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.type-btn span {
    font-size: 1.1rem;
    font-weight: 600;
}

.type-btn small {
    opacity: 0.8;
    font-size: 0.9rem;
}

.input-methods h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.input-options {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.input-btn {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    width: 150px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.input-btn:hover {
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.input-btn i {
    font-size: 1.5rem;
    color: #667eea;
}

.input-btn span {
    font-weight: 500;
    color: var(--text-primary);
}

/* Modern Card Styles */
.modern-card {
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    margin-bottom: 2rem;
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.card-header {
    padding: 24px 24px 16px 24px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.header-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    flex-shrink: 0;
}

.header-content {
    flex: 1;
    min-width: 0;
}

.card-title {
    margin: 0 0 4px 0;
    font-size: 1.4rem;
    font-weight: 600;
    color: #ffffff;
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card-subtitle {
    margin: 0;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;
}

.card-body {
    padding: 24px;
}

/* Model Selection Card Styles */
.model-selector-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.selector-wrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.selector-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
}

.selector-label i {
    color: #667eea;
}

.custom-select-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.modern-select {
    width: 100%;
    padding: 16px 50px 16px 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #ffffff;
    font-size: 1rem;
    font-weight: 500;
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.modern-select:hover {
    border-color: rgba(102, 126, 234, 0.5);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
}

.modern-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.modern-select option {
    background: #1a1a2e;
    color: #ffffff;
    padding: 12px;
}

.select-arrow {
    position: absolute;
    right: 16px;
    pointer-events: none;
    color: rgba(255, 255, 255, 0.6);
    transition: transform 0.3s ease;
}

.custom-select-wrapper:hover .select-arrow {
    color: #667eea;
    transform: translateY(-1px);
}

.model-status-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    backdrop-filter: blur(10px);
}

.status-indicator-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: relative;
    flex-shrink: 0;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

.status-unknown {
    background: #fbbf24;
}

.status-unknown::before {
    background: #fbbf24;
}

.status-available {
    background: #10b981;
}

.status-available::before {
    background: #10b981;
}

.status-error {
    background: #ef4444;
}

.status-error::before {
    background: #ef4444;
}

.status-text {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    font-size: 0.95rem;
}

.status-details {
    margin-top: 4px;
}

.status-description {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
    line-height: 1.4;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.1;
    }
}

/* Model Management Card Styles */
.management-grid {
    display: grid;
    gap: 24px;
}

.management-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 16px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

.section-title i {
    color: #667eea;
    font-size: 1rem;
}

.action-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.modern-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    color: #ffffff;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.btn-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
    flex: 1;
}

.btn-title {
    font-weight: 600;
    font-size: 0.95rem;
    color: #ffffff;
}

.btn-subtitle {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    line-height: 1.2;
}

/* Button Color Variants */
.btn-add .btn-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-add:hover .btn-icon {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: scale(1.1);
}

.btn-remove .btn-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn-remove:hover .btn-icon {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: scale(1.1);
}

.btn-view .btn-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.btn-view:hover .btn-icon {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: scale(1.1);
}

.btn-test .btn-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
}

.btn-test:hover .btn-icon {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    transform: scale(1.1);
}

.btn-api .btn-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.btn-api:hover .btn-icon {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: scale(1.1);
}

/* Question Settings Card Styles */
.settings-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.setting-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 24px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.setting-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.setting-card:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.setting-card:hover::before {
    opacity: 1;
}

.setting-icon-wrapper {
    display: flex;
    justify-content: center;
    margin-bottom: 16px;
}

.setting-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.setting-content {
    text-align: center;
}

.setting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.setting-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
}

.setting-value-display {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    min-width: 40px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.setting-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
    line-height: 1.4;
    margin: 0 0 20px 0;
}

.setting-input-wrapper {
    margin-top: 16px;
}

.modern-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.1);
    outline: none;
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modern-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
}

.modern-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.6);
}

.modern-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
}

.modern-slider::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.6);
}

.modern-slider::-webkit-slider-track {
    height: 6px;
    border-radius: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0.3;
}

.modern-slider::-moz-range-track {
    height: 6px;
    border-radius: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0.3;
    border: none;
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    padding: 0 10px;
}

.slider-labels span {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 500;
}

.setting-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.setting-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-select:hover {
    border-color: var(--text-secondary);
}

[data-theme="dark"] .setting-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%9ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

.model-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.8rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-available {
    background: #10b981;
    box-shadow: 0 0 6px rgba(16, 185, 129, 0.4);
}

.status-rate-limited {
    background: #f59e0b;
    box-shadow: 0 0 6px rgba(245, 158, 11, 0.4);
}

.status-unavailable {
    background: #ef4444;
    box-shadow: 0 0 6px rgba(239, 68, 68, 0.4);
}

.status-unknown {
    background: #6b7280;
    animation: pulse 2s infinite;
}

.status-text {
    color: var(--text-secondary);
}

/* Legacy styles removed - using modern card styles instead */

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.setting-item:hover {
    border-color: var(--border-hover);
    box-shadow: var(--shadow-light);
}

.setting-info {
    flex: 1;
}

.setting-info label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    cursor: pointer;
}

.setting-info label i {
    color: #667eea;
    font-size: 1.1rem;
}

.setting-description {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.3;
}

.setting-control {
    margin-left: 1rem;
}

.setting-input {
    width: 80px;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.setting-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-input:hover {
    border-color: var(--border-hover);
}

/* Content Screen */
.content-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
}

.screen-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.screen-header h2 {
    flex: 1;
    color: var(--text-primary);
}

.header-nav {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.quiz-nav {
    display: flex;
    align-items: center;
}

.input-area {
    display: none;
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
}

.input-area.active {
    display: block;
}

/* Text Input */
#textContent {
    width: 100%;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 200px;
    transition: border-color 0.3s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

#textContent:focus {
    outline: none;
    border-color: var(--border-focus);
}

/* Upload Zones */
.upload-zone {
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--bg-tertiary);
}

.upload-zone:hover {
    border-color: var(--border-hover);
    background: var(--bg-hover);
}

.upload-zone.dragover {
    border-color: var(--border-hover);
    background: var(--bg-accent);
    transform: scale(1.02);
}

.upload-zone i {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
    display: block;
}

.upload-zone p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.upload-zone small {
    color: var(--text-muted);
}

/* File Info */
.file-info {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.file-details {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.file-details i {
    color: #667eea;
}

/* Image Preview */
.image-preview {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
}

.image-preview img {
    max-width: 300px;
    max-height: 200px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.image-preview .btn {
    position: absolute;
    top: -10px;
    right: -10px;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Input Actions */
.input-actions {
    margin-top: 2rem;
    text-align: center;
}

/* ===== AI PROCESSING SCREEN ===== */
.ai-processing-container {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

/* Animated Background */
.processing-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    z-index: 1;
}

.neural-network {
    position: relative;
    width: 100%;
    height: 100%;
}

.node {
    position: absolute;
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: pulse 2s infinite ease-in-out;
}

.node-1 { top: 20%; left: 15%; animation-delay: 0s; }
.node-2 { top: 30%; right: 20%; animation-delay: 0.5s; }
.node-3 { bottom: 25%; left: 25%; animation-delay: 1s; }
.node-4 { bottom: 35%; right: 15%; animation-delay: 1.5s; }
.node-5 { top: 50%; left: 50%; animation-delay: 0.8s; }

.connection {
    position: absolute;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: flow 3s infinite linear;
}

.conn-1 { top: 22%; left: 16%; width: 200px; transform: rotate(15deg); }
.conn-2 { top: 32%; right: 22%; width: 150px; transform: rotate(-25deg); }
.conn-3 { bottom: 27%; left: 27%; width: 180px; transform: rotate(45deg); }
.conn-4 { bottom: 37%; right: 17%; width: 160px; transform: rotate(-35deg); }

/* Main Content */
.processing-content {
    position: relative;
    z-index: 10;
    text-align: center;
    color: white;
    max-width: 600px;
    padding: 2rem;
}

/* AI Brain Animation */
.ai-brain-container {
    margin-bottom: 3rem;
    position: relative;
}

.brain-core {
    position: relative;
    display: inline-block;
    width: 120px;
    height: 120px;
}

.brain-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: brainPulse 2s infinite ease-in-out;
}

.brain-pulse::before,
.brain-pulse::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: brainPulse 2s infinite ease-in-out;
}

.brain-pulse::before {
    width: 140px;
    height: 140px;
    animation-delay: 0.3s;
}

.brain-pulse::after {
    width: 160px;
    height: 160px;
    animation-delay: 0.6s;
}

.brain-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
    color: white;
    animation: float 3s ease-in-out infinite;
}

.thinking-dots {
    position: absolute;
    top: -20px;
    right: -20px;
    display: flex;
    gap: 4px;
}

.dot {
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: thinking 1.5s infinite ease-in-out;
}

.dot-1 { animation-delay: 0s; }
.dot-2 { animation-delay: 0.2s; }
.dot-3 { animation-delay: 0.4s; }

/* Title and Status */
.processing-info {
    margin-bottom: 3rem;
}

.processing-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.title-gradient {
    background: linear-gradient(45deg, #ffffff, #e0e7ff, #ffffff);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

.processing-status {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    animation: fadeInOut 2s ease-in-out infinite;
}

/* Advanced Progress Bar */
.progress-container {
    margin-bottom: 3rem;
}

.progress-track {
    position: relative;
    width: 100%;
    max-width: 400px;
    height: 12px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    margin: 0 auto 1rem;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00f5ff, #0080ff, #0040ff);
    background-size: 200% 100%;
    border-radius: 20px;
    width: 0%;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    animation: progressGlow 2s ease-in-out infinite;
}

.progress-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    border-radius: 20px;
    animation: progressSweep 2s linear infinite;
}

.progress-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: particleFloat 3s linear infinite;
}

.particle-1 { left: 20%; animation-delay: 0s; }
.particle-2 { left: 50%; animation-delay: 1s; }
.particle-3 { left: 80%; animation-delay: 2s; }

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 400px;
    margin: 0 auto;
}

.progress-percentage {
    font-size: 1.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.progress-eta {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Processing Steps */
.processing-steps {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.5;
    transition: all 0.5s ease;
}

.step.active {
    opacity: 1;
    transform: scale(1.1);
}

.step.completed {
    opacity: 0.8;
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.5s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.step.active .step-icon {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    animation: stepPulse 1s ease-in-out infinite;
}

.step.completed .step-icon {
    background: rgba(0, 255, 0, 0.3);
    color: #00ff00;
}

.step-text {
    font-size: 0.9rem;
    text-align: center;
    max-width: 80px;
}

/* Error Dialogs */
.error-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.dialog-content {
    background: var(--bg-primary);
    border-radius: 16px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    animation: dialogSlideIn 0.3s ease-out;
}

.dialog-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #ff6b6b;
}

.rate-limit-dialog .dialog-icon {
    color: #ffa726;
}

.dialog-content h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.5rem;
}

.dialog-content p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.dialog-content ul {
    text-align: left;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.dialog-content li {
    margin-bottom: 0.5rem;
}

.dialog-options {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.dialog-options .btn {
    min-width: 120px;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.dialog-options .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
}

.dialog-options .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.dialog-options .btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.dialog-options .btn-secondary:hover {
    background: var(--border-color);
    transform: translateY(-2px);
}

.retry-countdown {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 8px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    border: 1px solid var(--border-color);
}

.retry-countdown span {
    font-weight: 700;
    color: #ffa726;
    font-size: 1.1rem;
}

.demo-info {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.demo-info small {
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.demo-info i {
    color: #667eea;
}

/* Model Selection Dialog */
.model-selection-dialog .dialog-icon {
    color: #667eea;
}

.model-selection-container {
    margin: 1.5rem 0;
    text-align: left;
}

.model-selection-container label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 600;
}

.model-selection-container .setting-select {
    width: 100%;
    margin-bottom: 0;
}

@keyframes dialogSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Mobile responsive for dialogs */
@media (max-width: 768px) {
    .dialog-content {
        padding: 1.5rem;
        margin: 1rem;
    }

    .dialog-options {
        flex-direction: column;
    }

    .dialog-options .btn {
        width: 100%;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

/* Legacy model management styles removed - using modern card styles instead */
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
    padding: 20px;
    box-sizing: border-box;
    overflow: hidden;
}

.modal-content {
    background: var(--card-bg);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 100%;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    border: 1px solid var(--border-color);
    position: relative;
    margin: 0 auto;
    box-sizing: border-box;
}

.modal-large {
    max-width: min(700px, calc(100vw - 40px));
    width: 100%;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: var(--primary-gradient);
    border-radius: 16px 16px 0 0;
}

.modal-header h3 {
    color: white;
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.2s;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

.modal-body {
    padding: 24px;
    overflow-x: hidden;
    word-wrap: break-word;
}

.modal-body * {
    max-width: 100%;
    box-sizing: border-box;
}

.modal-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 20px 24px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: 0 0 16px 16px;
    flex-wrap: wrap;
}

/* Modal Responsive Styles */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 10px;
    }

    .modal-content {
        max-width: 100%;
        width: 100%;
        max-height: calc(100vh - 20px);
        margin: 0;
    }

    .modal-large {
        max-width: 100%;
        width: 100%;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .modal-overlay {
        padding: 5px;
    }

    .modal-content {
        max-height: calc(100vh - 10px);
        border-radius: 12px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 12px;
    }
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: var(--text-primary);
}

.form-input, .form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--input-bg);
    color: var(--text-primary);
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
    max-width: 100%;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-help {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: var(--text-secondary);
}

/* Model Info Styles */
.model-info {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    margin-top: 12px;
}

.info-item {
    margin-bottom: 8px;
    font-size: 14px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item strong {
    color: var(--text-primary);
}

/* Models List Styles */
.models-list {
    max-height: 400px;
    overflow-y: auto;
}

.model-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 12px;
    background: var(--bg-secondary);
    transition: background 0.2s;
}

.model-item:hover {
    background: var(--hover-bg);
}

.model-item:last-child {
    margin-bottom: 0;
}

.model-details {
    flex: 1;
}

.model-id {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 4px;
}

.model-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.model-description {
    font-size: 12px;
    color: var(--text-secondary);
}

.model-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-available {
    background: #10b981;
}

.status-rate-limited {
    background: #f59e0b;
}

.status-unavailable {
    background: #ef4444;
}

.status-unknown {
    background: #6b7280;
}

/* Model Testing Styles */
.test-controls {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.test-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;
}

.test-results {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
}

.test-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.test-placeholder i {
    font-size: 2rem;
    margin-bottom: 12px;
    opacity: 0.5;
}

.test-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    transition: background 0.2s;
}

.test-item:hover {
    background: var(--hover-bg);
}

.test-item:last-child {
    border-bottom: none;
}

.test-model-info {
    flex: 1;
}

.test-model-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.test-model-id {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--text-secondary);
}

.test-status {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: flex-end;
}

.test-status-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: white;
}

.test-status-testing {
    background: #3b82f6;
    animation: pulse 1.5s infinite;
}

.test-status-success {
    background: #10b981;
}

.test-status-failed {
    background: #ef4444;
}

.test-status-pending {
    background: #6b7280;
}

.test-timing {
    font-size: 11px;
    color: var(--text-secondary);
    margin-left: 8px;
}

.test-error {
    font-size: 11px;
    color: #ef4444;
    margin-top: 4px;
    max-width: 200px;
    word-wrap: break-word;
}

.test-success-info {
    font-size: 11px;
    color: #10b981;
    margin-top: 4px;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Notification Container */
.notification-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1001;
    max-width: 400px;
}

.notification {
    background: white;
    border-left: 4px solid #667eea;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    max-width: 350px;
    animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    word-wrap: break-word;
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.warning {
    border-left-color: #ffc107;
}

/* ===== ANIMATIONS ===== */
@keyframes pulse {
    0%, 100% {
        opacity: 0.4;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

@keyframes flow {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes brainPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.6;
    }
}

@keyframes float {
    0%, 100% { transform: translate(-50%, -50%) translateY(0px); }
    50% { transform: translate(-50%, -50%) translateY(-10px); }
}

@keyframes thinking {
    0%, 80%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    40% {
        opacity: 1;
        transform: scale(1.3);
    }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes progressGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(0, 245, 255, 0.8),
                    0 0 30px rgba(0, 128, 255, 0.6);
    }
}

@keyframes progressSweep {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(400%); }
}

@keyframes particleFloat {
    0% {
        transform: translateY(0px) translateX(0px);
        opacity: 0;
    }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% {
        transform: translateY(-20px) translateX(10px);
        opacity: 0;
    }
}

@keyframes stepPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes slideIn {
    from {
        transform: translateY(100%) translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0) translateX(0);
        opacity: 1;
    }
}

/* Questions Display */
.questions-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.question-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.question-actions .btn {
    min-width: 180px;
}

.questions-display {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
}

.question-item {
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 0;
}

.question-item:last-child {
    border-bottom: none;
}

.question-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.question-number {
    background: #667eea;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.question-text {
    flex: 1;
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-primary);
}

.question-options {
    margin-left: 3rem;
    margin-bottom: 1rem;
}

.option-item {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.option-letter {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
    color: var(--text-primary);
}

.option-item.correct .option-letter {
    background: #d4edda;
    border-color: #28a745;
    color: #28a745;
}

.question-answer {
    margin-left: 3rem;
    padding: 1rem;
    background: var(--success-bg);
    border-radius: 8px;
    border-left: 4px solid var(--success-color);
    color: var(--text-primary);
}

.answer-label {
    font-weight: 600;
    color: #28a745;
    margin-bottom: 0.5rem;
}

.question-explanation {
    margin-left: 3rem;
    margin-top: 1rem;
    padding: 1rem;
    background: var(--warning-bg);
    border-radius: 8px;
    border-left: 4px solid var(--warning-color);
    color: var(--text-primary);
}

.explanation-label {
    font-weight: 600;
    color: #856404;
    margin-bottom: 0.5rem;
}

/* Quiz Screen */
.quiz-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
    gap: 1rem;
}

.quiz-progress {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.quiz-score {
    font-size: 1.1rem;
    color: #667eea;
}

.quiz-content {
    flex: 1;
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    display: flex;
    flex-direction: column;
}

.quiz-question {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.quiz-options {
    flex: 1;
    margin-bottom: 2rem;
}

.quiz-option {
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--text-primary);
}

.quiz-option:hover {
    border-color: var(--border-hover);
    background: var(--bg-secondary);
    transform: var(--hover-transform);
}

.quiz-option.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.quiz-option.correct {
    border-color: var(--success-color);
    background: var(--success-bg);
    color: var(--text-primary);
}

.quiz-option.incorrect {
    border-color: var(--error-color);
    background: var(--error-bg);
    color: var(--text-primary);
}

.option-indicator {
    background: var(--bg-secondary);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.quiz-option.selected .option-indicator {
    background: white;
    color: #667eea;
    border: 1px solid #667eea;
}

.quiz-feedback {
    background: var(--bg-tertiary);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.feedback-correct {
    border-left: 4px solid var(--success-color);
    background: var(--success-bg);
    color: var(--text-primary);
}

.feedback-incorrect {
    border-left: 4px solid var(--error-color);
    background: var(--error-bg);
    color: var(--text-primary);
}

.feedback-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.feedback-correct .feedback-title {
    color: var(--text-primary);
}

.feedback-incorrect .feedback-title {
    color: var(--text-primary);
}

/* Ensure all feedback text is visible */
.quiz-feedback p,
.quiz-feedback span,
.quiz-feedback div {
    color: var(--text-primary) !important;
}

.feedback-correct p,
.feedback-correct span,
.feedback-correct div {
    color: var(--text-primary) !important;
}

.feedback-incorrect p,
.feedback-incorrect span,
.feedback-incorrect div {
    color: var(--text-primary) !important;
}

.quiz-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Results Screen */
.results-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

.results-header {
    margin-bottom: 3rem;
}

.results-icon {
    font-size: 4rem;
    color: #ffc107;
    margin-bottom: 1rem;
}

.results-display {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
}

.score-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.score-item {
    background: var(--bg-tertiary);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
}

.score-value {
    font-size: 2rem;
    font-weight: 600;
    color: #667eea;
    display: block;
}

.score-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.results-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* History Screen */
.history-content {
    padding: 2rem 0;
}

.history-filters {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    flex-wrap: wrap;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filter-select {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.history-stats-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.history-list {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.history-item {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
    cursor: pointer;
}

.history-item:hover {
    background: var(--bg-primary);
    transform: translateX(5px);
}

.history-item:last-child {
    border-bottom: none;
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.history-item-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
}

.history-item-title i {
    color: #667eea;
    font-size: 1.2rem;
}

.history-item-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.history-item-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.history-stat {
    text-align: center;
    padding: 0.75rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.history-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.history-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.score-excellent { color: #10b981; }
.score-good { color: #3b82f6; }
.score-average { color: #f59e0b; }
.score-poor { color: #ef4444; }

/* Statistics Screen */
.statistics-content {
    padding: 2rem 0;
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;
}

.stats-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    padding: 2rem;
}

.stats-section h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 0 1.5rem 0;
    color: var(--text-primary);
    font-size: 1.3rem;
}

.stats-section h3 i {
    color: #667eea;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    border-color: #667eea;
}

.stat-card.large {
    padding: 2rem;
    min-height: 140px;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-card.large .stat-icon {
    width: 80px;
    height: 80px;
}

.stat-icon i {
    color: white;
    font-size: 1.5rem;
}

.stat-card.large .stat-icon i {
    font-size: 2rem;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stat-card.large .stat-number {
    font-size: 3rem;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-sublabel {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-top: 0.25rem;
    opacity: 0.8;
}

.stat-progress {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.8s ease;
    width: 0%;
}

.progress-text {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-primary);
    min-width: 35px;
}

/* Achievements */
.achievements-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    padding: 2rem;
}

.achievements-section h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 0 1.5rem 0;
    color: var(--text-primary);
    font-size: 1.3rem;
}

.achievements-section h3 i {
    color: #f59e0b;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.achievement-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.achievement-card.unlocked {
    border-color: #f59e0b;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
}

.achievement-card.unlocked::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.achievement-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

.achievement-card.unlocked .achievement-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    transform: scale(1.1);
}

.achievement-icon i {
    font-size: 1.5rem;
    color: var(--text-secondary);
}

.achievement-card.unlocked .achievement-icon i {
    color: white;
}

.achievement-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.achievement-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.achievement-card.unlocked .achievement-title {
    color: #f59e0b;
}

/* Loading States */
.loading-placeholder {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.loading-placeholder i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #667eea;
}

.loading-placeholder p {
    margin: 0;
    font-size: 1.1rem;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--border-color);
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.empty-state p {
    margin: 0;
    line-height: 1.5;
}

/* Floating Back Button */
.floating-back-btn {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transform: scale(0);
    opacity: 0;
}

.floating-back-btn[style*="flex"] {
    transform: scale(1);
    opacity: 1;
    animation: bounceIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.floating-back-btn:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.floating-back-btn:active {
    transform: translateY(-1px) scale(1.05);
}

@keyframes bounceIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .type-buttons {
        flex-direction: column;
        align-items: center;
    }

    .input-options {
        flex-direction: column;
        align-items: center;
    }

    .quiz-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .question-actions {
        flex-direction: column;
    }

    .results-actions {
        flex-direction: column;
        align-items: center;
    }

    /* History & Statistics Mobile */
    .history-filters {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-group {
        min-width: auto;
    }

    .history-stats-summary {
        grid-template-columns: 1fr;
    }

    .history-item-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .stats-overview {
        grid-template-columns: 1fr;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    .achievements-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .stat-card.large {
        min-height: auto;
    }

    .stat-progress {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    /* Floating button mobile adjustments */
    .floating-back-btn {
        bottom: 20px;
        left: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    /* Notification container mobile adjustments */
    .notification-container {
        bottom: 10px;
        right: 10px;
        max-width: calc(100vw - 100px);
    }

    .notification {
        max-width: 100%;
        font-size: 0.9rem;
    }

    /* AI Processing Screen Mobile */
    .processing-content {
        padding: 1rem;
        max-width: 90%;
    }

    .processing-title {
        font-size: 2rem;
    }

    .brain-core {
        width: 80px;
        height: 80px;
    }

    .brain-pulse {
        width: 80px;
        height: 80px;
    }

    .brain-pulse::before {
        width: 100px;
        height: 100px;
    }

    .brain-pulse::after {
        width: 120px;
        height: 120px;
    }

    .brain-icon {
        font-size: 2rem;
    }

    .progress-track {
        max-width: 300px;
        height: 10px;
    }

    .progress-percentage {
        font-size: 1.2rem;
    }

    .processing-steps {
        gap: 1rem;
    }

    .step-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .step-text {
        font-size: 0.8rem;
        max-width: 60px;
    }

    .neural-network {
        display: none; /* Hide complex animations on mobile for performance */
    }
}

/* API Key Management Dialog Styles */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.api-key-dialog {
    width: 90%;
    max-width: 700px;
    max-height: 80vh;
    overflow-y: auto;
    background: var(--bg-primary);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    animation: dialogSlideIn 0.3s ease-out;
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: 16px 16px 0 0;
}

.dialog-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dialog-header h3 i {
    color: #667eea;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.dialog-body {
    padding: 2rem;
}

.dialog-footer {
    padding: 1rem 2rem;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: 0 0 16px 16px;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.api-key-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background: var(--bg-secondary);
}

.api-key-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.api-key-section h4 i {
    color: #667eea;
}

.api-key-info {
    background: var(--bg-primary);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.api-key-info-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.api-key-info-label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.api-key-info-value {
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    word-break: break-all;
}

.input-group {
    margin-bottom: 1rem;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.input-with-toggle {
    position: relative;
    display: flex;
    align-items: center;
}

.api-key-input {
    flex: 1;
    padding: 12px 45px 12px 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: border-color 0.2s;
}

.api-key-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.api-key-input.valid {
    border-color: #48bb78;
}

.api-key-input.invalid {
    border-color: #f56565;
}

.toggle-btn {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: color 0.2s;
}

.toggle-btn:hover {
    color: var(--text-primary);
    background: var(--border-color);
}

.help-text {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    line-height: 1.4;
}

.button-group {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.instructions-list {
    margin: 0;
    padding-left: 1.25rem;
    color: var(--text-primary);
}

.instructions-list li {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.instructions-list a {
    color: #667eea;
    text-decoration: none;
}

.instructions-list a:hover {
    text-decoration: underline;
}

.api-key-alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
    font-size: 0.9rem;
}

.api-key-alert.success {
    background: var(--success-bg);
    color: var(--text-primary);
    border: 1px solid #9ae6b4;
}

.api-key-alert.error {
    background: var(--error-bg);
    color: var(--text-primary);
    border: 1px solid #fc8181;
}

.api-key-alert.warning {
    background: var(--warning-bg);
    color: var(--text-primary);
    border: 1px solid #f6e05e;
}

.api-key-alert.info {
    background: var(--info-bg);
    color: var(--text-primary);
    border: 1px solid #90cdf4;
}

/* Mobile responsive for API key dialog */
@media (max-width: 768px) {
    .api-key-dialog {
        width: 95%;
        max-height: 90vh;
    }

    .dialog-header,
    .dialog-body,
    .dialog-footer {
        padding: 1rem;
    }

    .api-key-info-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .button-group {
        flex-direction: column;
    }

    .button-group .btn {
        width: 100%;
        justify-content: center;
    }
}
