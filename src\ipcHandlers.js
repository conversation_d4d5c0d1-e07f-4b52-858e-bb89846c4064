const { ipcMain, dialog, shell, Notification } = require('electron');
const path = require('path');
const fs = require('fs');

// Import existing services (adapted for desktop)
const messageHandlers = require('./handlers/messageHandlers');
const apiService = require('./services/apiService');
const fileService = require('./services/fileService');
const database = require('./database/database');
const logger = require('./utils/logger');

class IPCHandlers {
    constructor() {
        this.setupHandlers();
    }

    setupHandlers() {
        // File operations
        ipcMain.handle('select-file', async (event, options = {}) => {
            try {
                const defaultFilters = [
                    { name: 'All Supported', extensions: ['pdf', 'docx', 'doc', 'txt', 'jpg', 'jpeg', 'png', 'bmp', 'tiff', 'tif'] },
                    { name: 'Documents', extensions: ['pdf', 'docx', 'doc', 'txt'] },
                    { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'bmp', 'tiff', 'tif'] },
                    { name: 'PDF Files', extensions: ['pdf'] },
                    { name: 'Word Documents', extensions: ['docx', 'doc'] },
                    { name: 'Text Files', extensions: ['txt'] },
                    { name: 'All Files', extensions: ['*'] }
                ];

                const result = await dialog.showOpenDialog({
                    properties: ['openFile'],
                    filters: options.filters || defaultFilters,
                    title: options.title || 'Select a file to process'
                });

                if (!result.canceled && result.filePaths.length > 0) {
                    const filePath = result.filePaths[0];
                    logger.info(`File selected: ${filePath}`);
                    return { success: true, filePath: filePath };
                }

                return { success: false, error: 'No file selected' };
            } catch (error) {
                logger.error('Error in select-file:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('process-file', async (event, fileData) => {
            try {
                // Handle different types of file data
                let filePath;

                if (typeof fileData === 'string') {
                    // Direct file path
                    filePath = fileData;
                } else if (fileData && fileData.path) {
                    // File object with path property
                    filePath = fileData.path;
                } else if (fileData && fileData.filePath) {
                    // File object with filePath property
                    filePath = fileData.filePath;
                } else {
                    throw new Error('Invalid file data provided');
                }

                logger.info(`Processing file: ${filePath}`);

                if (!filePath || filePath === 'undefined') {
                    throw new Error('File path is undefined or invalid');
                }

                // Check if file exists
                if (!fs.existsSync(filePath)) {
                    throw new Error(`File not found: ${filePath}`);
                }

                // Use existing file service to extract text
                const result = await fileService.extractTextFromDocument(filePath);

                if (result && result.text) {
                    return {
                        success: true,
                        text: result.text,
                        pageCount: result.pageCount || 1,
                        questionCount: result.questionCount || 15
                    };
                } else {
                    throw new Error('Failed to extract text from file');
                }
            } catch (error) {
                logger.error('Error processing file:', error.message);
                return { success: false, error: error.message };
            }
        });

        // Question generation
        ipcMain.handle('generate-questions', async (event, content, type, count, preferredModel = 'auto') => {
            try {
                logger.info(`Generating ${count} ${type} questions`);
                logger.info(`IPC Handler: Received preferred model: "${preferredModel}" (type: ${typeof preferredModel})`);

                // If frontend sent 'auto', check if we have a saved preference in settings
                if (preferredModel === 'auto') {
                    try {
                        const settingsPath = path.join(__dirname, 'data', 'settings.json');
                        if (fs.existsSync(settingsPath)) {
                            const settingsData = fs.readFileSync(settingsPath, 'utf8');
                            const savedSettings = JSON.parse(settingsData);
                            if (savedSettings.preferredModel && savedSettings.preferredModel !== 'auto') {
                                preferredModel = savedSettings.preferredModel;
                                logger.info(`IPC Handler: Overriding with saved preference: "${preferredModel}"`);
                            }
                        }
                    } catch (settingsError) {
                        logger.warn(`Could not load settings in IPC handler: ${settingsError.message}`);
                    }
                }
                
                // Create a mock context object similar to Telegram context
                const mockContext = {
                    from: { id: 'desktop-user' },
                    chat: { id: 'desktop-chat' },
                    reply: (message) => {
                        logger.info('Bot reply:', message);
                        return Promise.resolve();
                    },
                    telegram: {
                        editMessageText: () => Promise.resolve()
                    }
                };

                // Use existing API service to generate questions
                const questions = await apiService.generateQuestionsFromAPI(
                    content,
                    type,
                    count,
                    2, // retries
                    false, // isScanned
                    'desktop-user',
                    'text', // contentType
                    preferredModel // pass the preferred model
                );

                if (questions && questions.length > 0) {
                    logger.success(`Generated ${questions.length} questions`);
                    return questions;
                } else {
                    throw new Error('No questions were generated');
                }
            } catch (error) {
                logger.error('Error generating questions:', error);
                throw error;
            }
        });

        // Quiz operations
        ipcMain.handle('start-quiz', async (event, questions) => {
            try {
                // Initialize quiz session in database if needed
                const quizSession = {
                    id: Date.now().toString(),
                    questions: questions,
                    startTime: new Date().toISOString(),
                    status: 'active'
                };

                // Store session (you might want to implement this in database)
                logger.info('Quiz session started');
                return { success: true, sessionId: quizSession.id };
            } catch (error) {
                logger.error('Error starting quiz:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('submit-answer', async (event, questionIndex, answer) => {
            try {
                // Process answer submission
                logger.info(`Answer submitted for question ${questionIndex}: ${answer}`);
                return { success: true };
            } catch (error) {
                logger.error('Error submitting answer:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-quiz-results', async (event) => {
            try {
                // Get quiz results from database
                return { success: true, results: {} };
            } catch (error) {
                logger.error('Error getting quiz results:', error);
                return { success: false, error: error.message };
            }
        });

        // Database operations
        ipcMain.handle('save-quiz-session', async (event, session) => {
            try {
                // Save quiz session to database
                const sessionData = {
                    timestamp: session.timestamp,
                    question_type: session.questionType,
                    score_correct: session.score.correct,
                    score_total: session.score.total,
                    duration: session.duration,
                    answers: JSON.stringify(session.answers),
                    questions: JSON.stringify(session.questions)
                };

                // Use existing database methods
                const db = database.getDatabase();
                const stmt = db.prepare(`
                    INSERT INTO quiz_sessions 
                    (timestamp, question_type, score_correct, score_total, duration, answers, questions)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                `);

                stmt.run(
                    sessionData.timestamp,
                    sessionData.question_type,
                    sessionData.score_correct,
                    sessionData.score_total,
                    sessionData.duration,
                    sessionData.answers,
                    sessionData.questions
                );

                logger.success('Quiz session saved to database');
                return { success: true };
            } catch (error) {
                logger.error('Error saving quiz session:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-quiz-history', async (event) => {
            try {
                const db = database.getDatabase();
                const stmt = db.prepare(`
                    SELECT * FROM quiz_sessions 
                    ORDER BY timestamp DESC 
                    LIMIT 50
                `);
                
                const sessions = stmt.all();
                
                // Parse JSON fields
                const parsedSessions = sessions.map(session => ({
                    ...session,
                    answers: JSON.parse(session.answers || '[]'),
                    questions: JSON.parse(session.questions || '[]')
                }));

                return { success: true, sessions: parsedSessions };
            } catch (error) {
                logger.error('Error getting quiz history:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-statistics', async (event) => {
            try {
                const db = database.getDatabase();
                
                // Get basic statistics
                const totalQuizzes = db.prepare('SELECT COUNT(*) as count FROM quiz_sessions').get();
                const avgScore = db.prepare('SELECT AVG(CAST(score_correct AS FLOAT) / score_total * 100) as avg FROM quiz_sessions').get();
                const totalQuestions = db.prepare('SELECT SUM(score_total) as total FROM quiz_sessions').get();
                
                const stats = {
                    totalQuizzes: totalQuizzes.count,
                    averageScore: Math.round(avgScore.avg || 0),
                    totalQuestions: totalQuestions.total || 0,
                    lastQuizDate: null
                };

                // Get last quiz date
                const lastQuiz = db.prepare('SELECT timestamp FROM quiz_sessions ORDER BY timestamp DESC LIMIT 1').get();
                if (lastQuiz) {
                    stats.lastQuizDate = lastQuiz.timestamp;
                }

                return { success: true, statistics: stats };
            } catch (error) {
                logger.error('Error getting statistics:', error);
                return { success: false, error: error.message };
            }
        });

        // API Key Management
        ipcMain.handle('get-api-key-info', async (event) => {
            try {
                const currentKey = process.env.API_KEY;

                if (!currentKey) {
                    return {
                        success: false,
                        error: 'No API key configured',
                        hasKey: false
                    };
                }

                // Mask the key for security (show first 15 and last 4 characters)
                const maskedKey = currentKey.length > 20
                    ? `${currentKey.substring(0, 15)}...${currentKey.substring(currentKey.length - 4)}`
                    : '***masked***';

                return {
                    success: true,
                    hasKey: true,
                    maskedKey: maskedKey,
                    keyLength: currentKey.length,
                    isValidFormat: currentKey.startsWith('sk-or-v1-') && currentKey.length === 73,
                    provider: 'OpenRouter.ai'
                };
            } catch (error) {
                logger.error('Error getting API key info:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('update-api-key', async (event, newApiKey) => {
            try {
                // Validate the new API key format
                if (!newApiKey || typeof newApiKey !== 'string') {
                    return { success: false, error: 'Invalid API key format' };
                }

                if (!newApiKey.startsWith('sk-or-v1-')) {
                    return { success: false, error: 'API key must start with "sk-or-v1-"' };
                }

                if (newApiKey.length !== 73) {
                    return { success: false, error: 'API key must be exactly 73 characters long' };
                }

                logger.info('Updating API key in both database and environment...');

                // Step 1: Update the database (primary source)
                const apiService = require('./services/apiService');

                try {
                    await apiService.addApiKey(newApiKey);
                    logger.info('✅ API key updated in database successfully');
                } catch (dbError) {
                    logger.error('❌ Failed to update database:', dbError.message);
                    return { success: false, error: `Database update failed: ${dbError.message}` };
                }

                // Step 2: Update the .env file (backup/sync)
                const envPath = path.join(__dirname, '..', '.env');
                let envContent = '';

                try {
                    if (fs.existsSync(envPath)) {
                        envContent = fs.readFileSync(envPath, 'utf8');
                    }
                } catch (readError) {
                    logger.warn('Could not read .env file, creating new one');
                }

                // Update or add the API_KEY line
                const lines = envContent.split('\n');
                let keyUpdated = false;

                for (let i = 0; i < lines.length; i++) {
                    if (lines[i].startsWith('API_KEY=')) {
                        lines[i] = `API_KEY=${newApiKey}`;
                        keyUpdated = true;
                        break;
                    }
                }

                if (!keyUpdated) {
                    lines.push(`API_KEY=${newApiKey}`);
                }

                // Write back to .env file
                try {
                    fs.writeFileSync(envPath, lines.join('\n'), 'utf8');
                    logger.info('✅ API key updated in .env file successfully');
                } catch (writeError) {
                    logger.warn('⚠️  Could not update .env file:', writeError.message);
                }

                // Step 3: Update the environment variable for current session
                process.env.API_KEY = newApiKey;

                logger.info('🎉 API key updated successfully in all locations');
                return {
                    success: true,
                    message: 'API key updated successfully! Database and .env file are now synchronized.'
                };

            } catch (error) {
                logger.error('Error updating API key:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('test-api-key', async (event, testKey = null) => {
            try {
                const keyToTest = testKey || process.env.API_KEY;

                if (!keyToTest) {
                    return { success: false, error: 'No API key to test' };
                }

                // Test the API key with a simple request
                const apiService = require('./services/apiService');

                logger.info('Testing API key with simple request...');

                const testResult = await apiService.generateQuestionsFromAPI(
                    "Test content for API key validation.",
                    "TF",
                    1,
                    0, // retries
                    false, // isScanned
                    'api-key-test',
                    'text',
                    null // Use any available model
                );

                if (testResult && Array.isArray(testResult) && testResult.length > 0) {
                    logger.info('API key test successful');
                    return {
                        success: true,
                        message: 'API key is working correctly',
                        questionsGenerated: testResult.length
                    };
                } else {
                    return {
                        success: false,
                        error: 'API key test failed - no questions generated'
                    };
                }

            } catch (error) {
                logger.error('API key test failed:', error);
                return {
                    success: false,
                    error: `API key test failed: ${error.message}`
                };
            }
        });

        // Settings
        ipcMain.handle('get-settings', async (event) => {
            try {
                const settingsPath = path.join(__dirname, 'data', 'settings.json');

                // Default settings
                const defaultSettings = {
                    questionsPerPage: 5,  // Questions per page for multi-page documents
                    imageQuestionsCount: 15,  // Total questions for images
                    questionType: 'MCQ',
                    autoSave: true,
                    notifications: true
                };

                try {
                    // Try to read existing settings
                    const fs = require('fs');
                    if (fs.existsSync(settingsPath)) {
                        const settingsData = fs.readFileSync(settingsPath, 'utf8');
                        const savedSettings = JSON.parse(settingsData);

                        // Merge with defaults to ensure all properties exist
                        const settings = { ...defaultSettings, ...savedSettings };
                        logger.info('Loaded settings from file:', settings);
                        return { success: true, settings };
                    }
                } catch (readError) {
                    logger.warn('Could not read settings file, using defaults:', readError.message);
                }

                // Return defaults if file doesn't exist or can't be read
                logger.info('Using default settings');
                return { success: true, settings: defaultSettings };
            } catch (error) {
                logger.error('Error getting settings:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('save-settings', async (event, newSettings) => {
            try {
                const settingsPath = path.join(__dirname, 'data', 'settings.json');
                const fs = require('fs');

                // Ensure data directory exists
                const dataDir = path.dirname(settingsPath);
                if (!fs.existsSync(dataDir)) {
                    fs.mkdirSync(dataDir, { recursive: true });
                }

                let currentSettings = {};

                // Load existing settings if they exist
                if (fs.existsSync(settingsPath)) {
                    try {
                        const settingsData = fs.readFileSync(settingsPath, 'utf8');
                        currentSettings = JSON.parse(settingsData);
                    } catch (readError) {
                        logger.warn('Could not read existing settings, starting fresh:', readError.message);
                    }
                }

                // Merge new settings with existing ones
                const updatedSettings = { ...currentSettings, ...newSettings };

                // Save to file
                fs.writeFileSync(settingsPath, JSON.stringify(updatedSettings, null, 2), 'utf8');

                logger.info('Settings saved to file:', JSON.stringify(updatedSettings, null, 2));
                return { success: true };
            } catch (error) {
                logger.error('Error saving settings:', error);
                return { success: false, error: error.message };
            }
        });

        // Feedback
        ipcMain.handle('submit-feedback', async (event, feedback) => {
            try {
                // Save feedback using existing feedback service
                const feedbackService = require('./services/feedbackService');
                const feedbackData = {
                    userId: 'desktop-user',
                    username: 'Desktop User',
                    rating: feedback.rating,
                    suggestion: feedback.suggestion,
                    quizType: feedback.quizType || 'Unknown',
                    score: feedback.score || 0
                };

                const saved = await feedbackService.saveFeedback(feedbackData);
                
                if (saved) {
                    logger.success('Feedback saved successfully');
                    return { success: true };
                } else {
                    throw new Error('Failed to save feedback');
                }
            } catch (error) {
                logger.error('Error submitting feedback:', error);
                return { success: false, error: error.message };
            }
        });

        // Utility functions
        ipcMain.handle('show-notification', async (event, title, body) => {
            try {
                if (Notification.isSupported()) {
                    new Notification({ title, body }).show();
                }
                return { success: true };
            } catch (error) {
                logger.error('Error showing notification:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('open-external', async (event, url) => {
            try {
                await shell.openExternal(url);
                return { success: true };
            } catch (error) {
                logger.error('Error opening external URL:', error);
                return { success: false, error: error.message };
            }
        });

        // File save dialog
        ipcMain.handle('save-file', async (event, options) => {
            try {
                const { dialog } = require('electron');
                const result = await dialog.showSaveDialog(options);

                if (result.canceled) {
                    return { success: false, canceled: true };
                } else {
                    return { success: true, filePath: result.filePath };
                }
            } catch (error) {
                logger.error('Error showing save dialog:', error);
                return { success: false, error: error.message };
            }
        });

        // PDF generation and save
        ipcMain.handle('save-pdf', async (event, filePath, htmlContent) => {
            try {
                const fs = require('fs').promises;
                const { BrowserWindow } = require('electron');

                // Create a hidden browser window for PDF generation
                const pdfWindow = new BrowserWindow({
                    show: false,
                    webPreferences: {
                        nodeIntegration: false,
                        contextIsolation: true
                    }
                });

                // Load the HTML content
                await pdfWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

                // Generate PDF
                const pdfBuffer = await pdfWindow.webContents.printToPDF({
                    format: 'A4',
                    margin: {
                        top: 20,
                        right: 15,
                        bottom: 20,
                        left: 15
                    },
                    printBackground: true,
                    landscape: false
                });

                // Close the window
                pdfWindow.close();

                // Save PDF to file
                await fs.writeFile(filePath, pdfBuffer);

                logger.success(`PDF saved to: ${filePath}`);
                return { success: true };
            } catch (error) {
                logger.error('Error generating PDF:', error);
                return { success: false, error: error.message };
            }
        });

        // Model Management
        ipcMain.handle('add-model', async (event, modelData) => {
            try {
                logger.info(`Adding new model: ${modelData.id}`);

                // Read current models configuration
                const modelsPath = path.join(__dirname, 'config', 'models.json');
                let models = [];

                // Ensure config directory exists
                const configDir = path.dirname(modelsPath);
                if (!fs.existsSync(configDir)) {
                    fs.mkdirSync(configDir, { recursive: true });
                }

                if (fs.existsSync(modelsPath)) {
                    const modelsData = fs.readFileSync(modelsPath, 'utf8');
                    models = JSON.parse(modelsData);
                }

                // Check if model already exists
                const existingModel = models.find(m => m.id === modelData.id);
                if (existingModel) {
                    return { success: false, error: 'Model already exists' };
                }

                // Add new model
                const newModel = {
                    id: modelData.id,
                    name: modelData.name,
                    description: modelData.description || '',
                    added: new Date().toISOString(),
                    custom: true
                };

                models.push(newModel);

                // Save updated models
                fs.writeFileSync(modelsPath, JSON.stringify(models, null, 2));

                logger.info(`Model ${modelData.id} added successfully`);
                return { success: true };

            } catch (error) {
                logger.error('Error adding model:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('remove-model', async (event, modelId) => {
            try {
                logger.info(`Removing model: ${modelId}`);

                // No restrictions - allow removal of ANY model

                // Read current removed models list
                const removedModelsPath = path.join(__dirname, 'config', 'removed-models.json');
                let removedModels = [];

                // Ensure config directory exists
                const configDir = path.dirname(removedModelsPath);
                if (!fs.existsSync(configDir)) {
                    fs.mkdirSync(configDir, { recursive: true });
                }

                if (fs.existsSync(removedModelsPath)) {
                    const removedData = fs.readFileSync(removedModelsPath, 'utf8');
                    removedModels = JSON.parse(removedData);
                }

                // Add model to removed list if not already there
                if (!removedModels.includes(modelId)) {
                    removedModels.push(modelId);
                    fs.writeFileSync(removedModelsPath, JSON.stringify(removedModels, null, 2));
                }

                // Also remove from custom models if it exists there
                const customModelsPath = path.join(__dirname, 'config', 'models.json');
                if (fs.existsSync(customModelsPath)) {
                    const customModelsData = fs.readFileSync(customModelsPath, 'utf8');
                    let customModels = JSON.parse(customModelsData);

                    const modelIndex = customModels.findIndex(m => m.id === modelId);
                    if (modelIndex !== -1) {
                        customModels.splice(modelIndex, 1);
                        fs.writeFileSync(customModelsPath, JSON.stringify(customModels, null, 2));
                    }
                }

                logger.info(`Model ${modelId} removed successfully`);
                return { success: true };

            } catch (error) {
                logger.error('Error removing model:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-all-models', async (event) => {
            try {
                // Read custom models first
                const customModelsPath = path.join(__dirname, 'config', 'models.json');
                let customModels = [];

                if (fs.existsSync(customModelsPath)) {
                    const modelsData = fs.readFileSync(customModelsPath, 'utf8');
                    customModels = JSON.parse(modelsData);
                }

                // If custom models exist, use ONLY custom models (user preference)
                if (customModels.length > 0) {
                    logger.info(`Using ${customModels.length} custom models only`);
                    return { success: true, models: customModels };
                }

                // No fallback models - user must add models through the UI
                logger.info('No custom models found - user must add models through the UI');
                return { success: true, models: [] };

            } catch (error) {
                logger.error('Error getting models:', error);
                return { success: false, error: error.message };
            }
        });

        // Model Testing
        ipcMain.handle('test-model', async (event, modelId, content, questionType, questionCount) => {
            try {
                logger.info(`Testing model: ${modelId}`);

                // Use the existing question generation logic
                const result = await apiService.generateQuestionsFromAPI(
                    content,
                    questionType,
                    questionCount,
                    'desktop-user',
                    modelId // Force specific model
                );

                if (result.success) {
                    logger.info(`Model ${modelId} test successful: ${result.questions.length} questions generated`);
                    return {
                        success: true,
                        questions: result.questions,
                        model: modelId
                    };
                } else {
                    logger.warn(`Model ${modelId} test failed: ${result.error}`);
                    return {
                        success: false,
                        error: result.error,
                        model: modelId
                    };
                }

            } catch (error) {
                logger.error(`Error testing model ${modelId}:`, error);
                return {
                    success: false,
                    error: error.message,
                    model: modelId
                };
            }
        });

        // Run Model Simulation
        ipcMain.handle('run-model-simulation', async (event, testParams) => {
            try {
                logger.info('Starting automated model simulation...');

                const { content, questionType, questionCount } = testParams;

                // Get all available models from custom models only
                const customModelsPath = path.join(__dirname, 'config', 'models.json');
                let availableModels = [];

                if (fs.existsSync(customModelsPath)) {
                    const customModelsData = fs.readFileSync(customModelsPath, 'utf8');
                    const customModels = JSON.parse(customModelsData);
                    availableModels = customModels;
                }

                logger.info(`Testing ${availableModels.length} models...`);

                const results = [];

                // Test each model
                for (let i = 0; i < availableModels.length; i++) {
                    const model = availableModels[i];
                    logger.info(`Testing model ${i + 1}/${availableModels.length}: ${model.id}`);

                    const startTime = Date.now();

                    try {
                        // Test the model
                        const result = await apiService.generateQuestionsFromAPI(
                            content,
                            questionType,
                            questionCount,
                            'desktop-user',
                            model.id
                        );

                        const endTime = Date.now();
                        const duration = endTime - startTime;

                        if (result.success && result.questions && result.questions.length > 0) {
                            logger.info(`✓ Model ${model.id} SUCCESS: ${result.questions.length} questions in ${duration}ms`);
                            results.push({
                                model: model,
                                success: true,
                                duration: duration,
                                questionsGenerated: result.questions.length,
                                questions: result.questions
                            });
                        } else {
                            logger.warn(`✗ Model ${model.id} FAILED: ${result.error || 'No questions generated'}`);
                            results.push({
                                model: model,
                                success: false,
                                duration: duration,
                                error: result.error || 'No questions generated'
                            });
                        }

                    } catch (error) {
                        const endTime = Date.now();
                        const duration = endTime - startTime;

                        logger.error(`✗ Model ${model.id} ERROR: ${error.message}`);
                        results.push({
                            model: model,
                            success: false,
                            duration: duration,
                            error: error.message
                        });
                    }

                    // Small delay between tests
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                // Generate summary
                const successCount = results.filter(r => r.success).length;
                const totalCount = results.length;
                const avgDuration = Math.round(
                    results.reduce((sum, r) => sum + r.duration, 0) / results.length
                );

                logger.info(`Simulation complete: ${successCount}/${totalCount} models working, avg ${avgDuration}ms`);

                return {
                    success: true,
                    results: results,
                    summary: {
                        totalModels: totalCount,
                        successfulModels: successCount,
                        failedModels: totalCount - successCount,
                        averageDuration: avgDuration
                    }
                };

            } catch (error) {
                logger.error('Error running model simulation:', error);
                return {
                    success: false,
                    error: error.message
                };
            }
        });
    }

    // Initialize database tables for desktop app
    initializeDesktopTables() {
        try {
            const db = database.getDatabase();

            if (!db) {
                logger.warn('Database not available, skipping desktop table initialization');
                return;
            }

            // Create quiz sessions table if it doesn't exist
            db.exec(`
                CREATE TABLE IF NOT EXISTS quiz_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    question_type TEXT NOT NULL,
                    score_correct INTEGER NOT NULL,
                    score_total INTEGER NOT NULL,
                    duration INTEGER NOT NULL,
                    answers TEXT,
                    questions TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // Initialize API key in database if it doesn't exist
            this.initializeApiKeyInDatabase();

            logger.success('Desktop database tables initialized');
        } catch (error) {
            logger.error('Error initializing desktop tables:', error.message);
            // Don't throw error, just log it
        }
    }

    // Initialize API key in database from environment
    async initializeApiKeyInDatabase() {
        try {
            const currentApiKey = process.env.API_KEY;

            if (!currentApiKey) {
                logger.warn('No API key found in environment variables');
                return;
            }

            // Check if we already have this key in the database
            const apiService = require('./services/apiService');
            const db = database.getDatabase();

            if (!db) {
                logger.warn('Database not available for API key initialization');
                return;
            }

            // Check if any API keys exist in database
            db.get('SELECT COUNT(*) as count FROM api_keys WHERE is_active = 1', [], async (err, row) => {
                if (err) {
                    logger.error('Error checking API keys in database:', err);
                    return;
                }

                if (row.count === 0) {
                    // No active API keys in database, add the current environment key
                    try {
                        await apiService.addApiKey(currentApiKey);
                        logger.info('Initialized database with current environment API key');
                    } catch (addError) {
                        logger.warn('Could not add environment API key to database:', addError.message);
                    }
                } else {
                    logger.info('Database already has active API keys');
                }
            });

        } catch (error) {
            logger.error('Error initializing API key in database:', error.message);
        }
    }
}

module.exports = IPCHandlers;
