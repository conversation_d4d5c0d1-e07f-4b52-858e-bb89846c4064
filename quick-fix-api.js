const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

console.log('🚀 Quick API Key Fix');
console.log('=' .repeat(50));

// Your current API key from .env
const currentKey = process.env.API_KEY || '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';

console.log('📋 Current API Key Status:');
console.log(`   Key: ${currentKey.substring(0, 15)}...${currentKey.substring(currentKey.length - 4)}`);
console.log(`   Length: ${currentKey.length}`);
console.log(`   Format: ${currentKey.startsWith('sk-or-v1-') && currentKey.length === 73 ? 'Valid' : 'Invalid'}`);

console.log('\n🔧 Fixing database and environment...');

// Update .env file to ensure it's correct
const envPath = path.join(__dirname, '.env');
let envContent = '';

try {
    if (fs.existsSync(envPath)) {
        envContent = fs.readFileSync(envPath, 'utf8');
    }
} catch (error) {
    console.log('⚠️  Could not read .env file');
}

// Update the API_KEY line
const lines = envContent.split('\n');
let keyUpdated = false;

for (let i = 0; i < lines.length; i++) {
    if (lines[i].startsWith('API_KEY=')) {
        lines[i] = `API_KEY=${currentKey}`;
        keyUpdated = true;
        break;
    }
}

if (!keyUpdated) {
    lines.push(`API_KEY=${currentKey}`);
}

// Write back to .env file
fs.writeFileSync(envPath, lines.join('\n'), 'utf8');
console.log('✅ Updated .env file');

// Update database
const dbPath = path.join(__dirname, 'data', 'bot.db');

if (fs.existsSync(dbPath)) {
    const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
            console.error('❌ Database connection error:', err.message);
            return;
        }
        
        console.log('🔧 Updating database...');
        
        // Clear all existing keys
        db.run('DELETE FROM api_keys', (err) => {
            if (err) {
                console.log('⚠️  Could not clear old keys:', err.message);
            } else {
                console.log('✅ Cleared old API keys');
            }
            
            // Add the current key
            db.run('INSERT INTO api_keys (key, is_active, last_used, created_at) VALUES (?, 1, ?, ?)', 
                [currentKey, Date.now(), Date.now()], function(err) {
                    if (err) {
                        console.log('❌ Could not add key to database:', err.message);
                    } else {
                        console.log(`✅ Added API key to database (ID: ${this.lastID})`);
                    }
                    
                    db.close();
                    
                    console.log('\n🎯 Next Steps:');
                    console.log('1. If the API key is still not working, you need a new one from OpenRouter');
                    console.log('2. Visit: https://openrouter.ai/settings/keys');
                    console.log('3. Create a new API key');
                    console.log('4. Run: node test-new-api-key.js');
                    console.log('5. Or manually update the .env file with the new key');
                    
                    console.log('\n🚀 Try starting the app now: npm start');
                });
        });
    });
} else {
    console.log('⚠️  Database not found');
    console.log('✅ The app will create it automatically when you start it');
}

// Also provide instructions for getting a new key
console.log('\n📋 If you need a new API key:');
console.log('1. Go to https://openrouter.ai/');
console.log('2. Sign in or create account');
console.log('3. Go to Settings → API Keys');
console.log('4. Create new key');
console.log('5. Update .env file: API_KEY=your_new_key');
console.log('6. Restart the application');
