const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

console.log('🔍 FINAL VERIFICATION: No Hardcoded Models');
console.log('=' .repeat(60));

// Model patterns to search for
const modelPatterns = [
    'google/gemini', 'google/gemma', 'nvidia/llama', 
    'deepseek/', 'qwen/', 'meta-llama/', 'mistralai/'
];

// Files to check (excluding user config files)
const filesToCheck = [
    'src/config/desktop.js',
    'src/config.js',
    'src/ipcHandlers.js',
    'run-simulation.js'
    // Note: models.json and removed-models.json are user config files, not hardcoded
];

console.log('\n📋 Step 1: Checking Source Files');

let hardcodedFound = false;

filesToCheck.forEach(filePath => {
    console.log(`\n   Checking: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
        console.log('      ⚠️  File not found');
        return;
    }
    
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let foundInFile = false;
        
        modelPatterns.forEach(pattern => {
            if (content.includes(pattern)) {
                console.log(`      ❌ Found hardcoded model: ${pattern}`);
                foundInFile = true;
                hardcodedFound = true;
            }
        });
        
        if (!foundInFile) {
            console.log('      ✅ No hardcoded models found');
        }
        
    } catch (error) {
        console.log(`      ❌ Error reading file: ${error.message}`);
    }
});

console.log('\n📋 Step 2: Checking Database');

const dbPath = path.join(__dirname, 'data', 'bot.db');

if (!fs.existsSync(dbPath)) {
    console.log('   ❌ Database file not found');
} else {
    const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
            console.error('   ❌ Database connection error:', err.message);
            return;
        }
        
        // Check model_stats table
        db.all('SELECT DISTINCT model FROM model_stats', [], (err, rows) => {
            if (err) {
                console.error('   ❌ Error checking model_stats:', err.message);
            } else {
                console.log(`   📊 model_stats table: ${rows.length} distinct models`);
                
                let dbHardcodedFound = false;
                rows.forEach(row => {
                    modelPatterns.forEach(pattern => {
                        if (row.model.includes(pattern)) {
                            console.log(`      ❌ Found hardcoded model in DB: ${row.model}`);
                            dbHardcodedFound = true;
                            hardcodedFound = true;
                        }
                    });
                });
                
                if (!dbHardcodedFound && rows.length > 0) {
                    console.log('      ✅ No hardcoded models in database');
                    rows.forEach((row, index) => {
                        console.log(`         ${index + 1}. ${row.model}`);
                    });
                } else if (rows.length === 0) {
                    console.log('      ✅ Database is clean (no model entries)');
                }
            }
            
            // Check config table
            db.all("SELECT key, value FROM config WHERE value LIKE '%google/%' OR value LIKE '%deepseek/%' OR value LIKE '%nvidia/%'", [], (err, configRows) => {
                if (err) {
                    console.error('   ❌ Error checking config table:', err.message);
                } else {
                    if (configRows.length > 0) {
                        console.log(`   📊 config table: ${configRows.length} model references`);
                        configRows.forEach(row => {
                            console.log(`      ⚠️  ${row.key}: ${row.value}`);
                        });
                    } else {
                        console.log('   ✅ config table: No model references');
                    }
                }
                
                db.close();
                showFinalResult();
            });
        });
    });
}

function showFinalResult() {
    console.log('\n📋 Step 3: Current Model Configuration');
    
    // Check current custom models
    const customModelsPath = path.join(__dirname, 'src', 'config', 'models.json');
    if (fs.existsSync(customModelsPath)) {
        try {
            const customModels = JSON.parse(fs.readFileSync(customModelsPath, 'utf8'));
            console.log(`   📊 Custom models: ${customModels.length}`);
            
            if (customModels.length > 0) {
                customModels.forEach((model, index) => {
                    console.log(`      ${index + 1}. ${model.name} (${model.id})`);
                });
            } else {
                console.log('      ⚠️  No custom models configured');
            }
        } catch (error) {
            console.log(`   ❌ Error reading custom models: ${error.message}`);
        }
    } else {
        console.log('   ⚠️  No custom models file found');
    }
    
    // Check removed models
    const removedModelsPath = path.join(__dirname, 'src', 'config', 'removed-models.json');
    if (fs.existsSync(removedModelsPath)) {
        try {
            const removedModels = JSON.parse(fs.readFileSync(removedModelsPath, 'utf8'));
            console.log(`   📊 Removed models: ${removedModels.length}`);
        } catch (error) {
            console.log(`   ❌ Error reading removed models: ${error.message}`);
        }
    }
    
    console.log('\n🎯 FINAL RESULT:');
    
    if (!hardcodedFound) {
        console.log('✅ SUCCESS: NO HARDCODED MODELS FOUND!');
        console.log('✅ All hardcoded models have been eliminated');
        console.log('✅ Application will use ONLY user-configured models');
        console.log('✅ Database is clean of hardcoded model references');
        console.log('✅ Config files contain no hardcoded models');
        
        console.log('\n🎯 What This Means:');
        console.log('   1. ✅ App shows ONLY models you explicitly add');
        console.log('   2. ✅ No surprise models appearing in dropdowns');
        console.log('   3. ✅ Complete control over model selection');
        console.log('   4. ✅ Clean, user-driven model management');
        
        console.log('\n🚀 Next Steps:');
        console.log('   1. Restart your application: npm start');
        console.log('   2. Add your desired models using "Add Model" button');
        console.log('   3. Only YOUR models will appear in the dropdown');
        console.log('   4. No more unwanted hardcoded models!');
        
    } else {
        console.log('❌ HARDCODED MODELS STILL FOUND!');
        console.log('   Please check the files marked with ❌ above');
        console.log('   Manual cleanup may be required');
    }
}

// If database check doesn't run, show result anyway
setTimeout(() => {
    if (!hardcodedFound) {
        // Database check might not have run, show file-only result
    }
}, 5000);
