const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const axios = require('axios');

// Load environment variables
require('dotenv').config();

console.log('🧪 Testing Single API Key System');
console.log('=' .repeat(50));

const envApiKey = process.env.API_KEY;

console.log('\n📋 System Status:');
console.log(`   .env API Key: ${envApiKey ? envApiKey.substring(0, 15) + '...' + envApiKey.substring(envApiKey.length - 4) : 'Not found'}`);

// Test database
const dbPath = path.join(__dirname, 'data', 'bot.db');

if (!fs.existsSync(dbPath)) {
    console.log('❌ Database file not found');
    process.exit(1);
}

const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ Database connection error:', err.message);
        process.exit(1);
    }
    
    console.log('✅ Connected to database');
    
    // Check database keys
    db.all('SELECT id, key, is_active FROM api_keys ORDER BY created_at DESC', [], (err, rows) => {
        if (err) {
            console.error('❌ Error fetching keys:', err.message);
            db.close();
            return;
        }
        
        console.log(`\n📋 Database API Keys: ${rows.length} total`);
        
        const activeKeys = rows.filter(row => row.is_active === 1);
        console.log(`   Active Keys: ${activeKeys.length}`);
        
        if (activeKeys.length === 0) {
            console.log('❌ No active keys in database');
            db.close();
            return;
        }
        
        if (activeKeys.length > 1) {
            console.log('⚠️  Multiple active keys found - this should not happen');
            activeKeys.forEach((key, index) => {
                const maskedKey = key.key.substring(0, 15) + '...' + key.key.substring(key.key.length - 4);
                console.log(`   ${index + 1}. ${maskedKey} (ID: ${key.id})`);
            });
        } else {
            const activeKey = activeKeys[0];
            const maskedKey = activeKey.key.substring(0, 15) + '...' + activeKey.key.substring(activeKey.key.length - 4);
            console.log(`   ✅ Single active key: ${maskedKey} (ID: ${activeKey.id})`);
            
            // Check if it matches .env
            if (activeKey.key === envApiKey) {
                console.log('   ✅ Database key matches .env key');
            } else {
                console.log('   ⚠️  Database key differs from .env key');
            }
        }
        
        // Test the active key
        if (activeKeys.length > 0) {
            console.log('\n🧪 Testing active API key...');
            
            const testKey = activeKeys[0].key;
            
            axios.post('https://openrouter.ai/api/v1/chat/completions', {
                model: 'google/gemini-2.0-flash-exp:free',
                messages: [{ role: 'user', content: 'Test' }],
                max_tokens: 5
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${testKey}`,
                    'HTTP-Referer': 'https://telegram-mcq-tf-bot.com',
                    'X-Title': 'Telegram MCQ/TF Question Generator'
                },
                timeout: 10000
            }).then(response => {
                console.log('✅ API Key Test: SUCCESS!');
                console.log(`   Status: ${response.status}`);
                
                console.log('\n🎉 SYSTEM STATUS: PERFECT!');
                console.log('✅ Single active API key in database');
                console.log('✅ API key is working correctly');
                console.log('✅ Database and .env are synchronized');
                
                console.log('\n📋 How the system now works:');
                console.log('1. 🔑 App uses database API key as primary source');
                console.log('2. 🔄 When you update key in app, it updates both database AND .env');
                console.log('3. 🛡️  Only ONE active key exists at any time');
                console.log('4. 🔧 No more duplicate keys or sync issues');
                
                console.log('\n🚀 Your application is ready to use!');
                console.log('   Run: npm start');
                
                db.close();
            }).catch(error => {
                console.log('❌ API Key Test: FAILED');
                console.log(`   Error: ${error.message}`);
                
                if (error.response && error.response.status === 401) {
                    console.log('\n💡 The API key is invalid. You need to:');
                    console.log('1. Get a new API key from https://openrouter.ai/settings/keys');
                    console.log('2. Update it in the app using the API Key Manager');
                    console.log('3. The app will automatically sync database and .env file');
                }
                
                db.close();
            });
        } else {
            db.close();
        }
    });
});
