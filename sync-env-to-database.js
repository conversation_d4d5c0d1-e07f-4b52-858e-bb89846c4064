const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// Load environment variables
require('dotenv').config();

console.log('🔄 Syncing .env API Key to Database');
console.log('=' .repeat(50));

const envApiKey = process.env.API_KEY;
console.log('\n📋 Environment API Key:');
console.log(`   Exists: ${!!envApiKey}`);
if (envApiKey) {
    console.log(`   Length: ${envApiKey.length}`);
    console.log(`   Format: ${envApiKey.startsWith('sk-or-v1-') ? 'Valid' : 'Invalid'}`);
    console.log(`   Masked: ${envApiKey.substring(0, 15)}...${envApiKey.substring(envApiKey.length - 4)}`);
}

if (!envApiKey) {
    console.log('❌ No API key found in .env file');
    console.log('💡 Please update your .env file with: API_KEY=your_new_key');
    process.exit(1);
}

// Connect to database
const dbPath = path.join(__dirname, 'data', 'bot.db');
console.log(`\n📋 Database: ${dbPath}`);

if (!fs.existsSync(dbPath)) {
    console.log('❌ Database file not found');
    process.exit(1);
}

const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ Database connection error:', err.message);
        process.exit(1);
    }
    
    console.log('✅ Connected to database');
    
    // Check current database API keys
    console.log('\n📋 Current Database API Keys:');
    db.all('SELECT id, key, is_active, last_used FROM api_keys ORDER BY id DESC', [], (err, rows) => {
        if (err) {
            console.error('❌ Error fetching keys:', err.message);
            db.close();
            return;
        }
        
        console.log(`   Count: ${rows.length}`);
        
        if (rows.length > 0) {
            rows.forEach((row, index) => {
                const maskedKey = row.key.substring(0, 15) + '...' + row.key.substring(row.key.length - 4);
                console.log(`   Key ${index + 1}: ${maskedKey} (Active: ${row.is_active}, ID: ${row.id})`);
            });
        }
        
        // Check if the environment key is already in database
        const existingKey = rows.find(row => row.key === envApiKey);
        
        if (existingKey) {
            console.log('\n✅ Environment API key already exists in database');
            
            if (existingKey.is_active === 1) {
                console.log('✅ Key is already active');
                testAndClose();
            } else {
                console.log('🔧 Activating the environment key...');
                
                // Deactivate all keys first
                db.run('UPDATE api_keys SET is_active = 0', (err) => {
                    if (err) {
                        console.error('❌ Error deactivating keys:', err.message);
                        db.close();
                        return;
                    }
                    
                    // Activate the environment key
                    db.run('UPDATE api_keys SET is_active = 1, last_used = ? WHERE id = ?', 
                        [Date.now(), existingKey.id], (err) => {
                            if (err) {
                                console.error('❌ Error activating key:', err.message);
                            } else {
                                console.log(`✅ Activated environment API key (ID: ${existingKey.id})`);
                            }
                            testAndClose();
                        });
                });
            }
        } else {
            console.log('\n🔧 Adding environment API key to database...');
            
            // Deactivate all existing keys
            db.run('UPDATE api_keys SET is_active = 0', (err) => {
                if (err) {
                    console.error('❌ Error deactivating old keys:', err.message);
                    db.close();
                    return;
                }
                
                console.log('✅ Deactivated all existing keys');
                
                // Add the environment key
                db.run('INSERT INTO api_keys (key, is_active, last_used, created_at) VALUES (?, 1, ?, ?)', 
                    [envApiKey, Date.now(), Date.now()], function(err) {
                        if (err) {
                            console.error('❌ Error adding API key:', err.message);
                        } else {
                            console.log(`✅ Added environment API key to database (ID: ${this.lastID})`);
                        }
                        testAndClose();
                    });
            });
        }
    });
    
    function testAndClose() {
        console.log('\n🧪 Testing API key...');
        
        const axios = require('axios');
        
        axios.post('https://openrouter.ai/api/v1/chat/completions', {
            model: 'google/gemini-2.0-flash-exp:free',
            messages: [{ role: 'user', content: 'Test' }],
            max_tokens: 5
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${envApiKey}`,
                'HTTP-Referer': 'https://telegram-mcq-tf-bot.com',
                'X-Title': 'Telegram MCQ/TF Question Generator'
            },
            timeout: 10000
        }).then(response => {
            console.log('✅ API Key Test: SUCCESS!');
            console.log(`   Status: ${response.status}`);
            
            console.log('\n🎉 SYNC COMPLETE!');
            console.log('✅ Database now uses the API key from your .env file');
            console.log('✅ Restart the application: npm start');
            console.log('✅ The 401 error should be resolved');
            
            db.close();
        }).catch(error => {
            console.log('❌ API Key Test: FAILED');
            console.log(`   Error: ${error.message}`);
            
            if (error.response && error.response.status === 401) {
                console.log('\n💡 The API key in your .env file is still invalid');
                console.log('🔧 You need to update your .env file with a working API key');
                console.log('📋 Steps:');
                console.log('   1. Go to https://openrouter.ai/settings/keys');
                console.log('   2. Create a new API key');
                console.log('   3. Update .env file: API_KEY=your_new_key');
                console.log('   4. Run this script again: node sync-env-to-database.js');
            }
            
            db.close();
        });
    }
});
