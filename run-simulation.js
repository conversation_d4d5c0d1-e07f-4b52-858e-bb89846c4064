const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// Import the IPC handlers
require('./src/ipcHandlers');

// Import API service
const apiService = require('./src/services/apiService');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'src', 'preload.js')
        },
        show: false // Don't show window for automated testing
    });

    mainWindow.loadFile('src/renderer/index.html');
}

async function runSimulation() {
    console.log('\n🧪 Starting Automated Model Simulation...\n');
    
    const testParams = {
        content: "The human heart has four chambers: two atria and two ventricles. Blood flows from the right atrium to the right ventricle, then to the lungs for oxygenation. The left atrium receives oxygenated blood from the lungs, which then flows to the left ventricle and is pumped throughout the body.",
        questionType: "MCQ",
        questionCount: 3
    };
    
    console.log('📋 Test Parameters:');
    console.log(`   Content: ${testParams.content.substring(0, 100)}...`);
    console.log(`   Question Type: ${testParams.questionType}`);
    console.log(`   Question Count: ${testParams.questionCount}`);
    console.log('\n🔄 Testing all available models...\n');
    
    try {
        // Run the simulation directly
        console.log('📊 Starting model tests...\n');

        // Get all available models from custom models only
        const customModelsPath = path.join(__dirname, 'src', 'config', 'models.json');
        let defaultModels = [];

        if (fs.existsSync(customModelsPath)) {
            const customModelsData = fs.readFileSync(customModelsPath, 'utf8');
            defaultModels = JSON.parse(customModelsData);
        }

        // Read removed models list
        const removedModelsPath = path.join(__dirname, 'src', 'config', 'removed-models.json');
        let removedModels = [];

        if (fs.existsSync(removedModelsPath)) {
            const removedData = fs.readFileSync(removedModelsPath, 'utf8');
            removedModels = JSON.parse(removedData);
        }

        // Filter out removed models
        const availableModels = defaultModels.filter(model =>
            !removedModels.includes(model.id)
        );

        console.log(`🎯 Testing ${availableModels.length} models:\n`);

        const results = [];

        // Test each model
        for (let i = 0; i < availableModels.length; i++) {
            const model = availableModels[i];
            console.log(`[${i + 1}/${availableModels.length}] Testing: ${model.name} (${model.id})`);

            const startTime = Date.now();

            try {
                // Test the model
                const result = await apiService.generateQuestionsFromAPI(
                    testParams.content,
                    testParams.questionType,
                    testParams.questionCount,
                    'desktop-user',
                    model.id
                );

                const endTime = Date.now();
                const duration = endTime - startTime;

                if (result.success && result.questions && result.questions.length > 0) {
                    console.log(`   ✅ SUCCESS: ${result.questions.length} questions generated in ${duration}ms`);
                    results.push({
                        model: model,
                        success: true,
                        duration: duration,
                        questionsGenerated: result.questions.length,
                        questions: result.questions
                    });
                } else {
                    console.log(`   ❌ FAILED: ${result.error || 'No questions generated'} (${duration}ms)`);
                    results.push({
                        model: model,
                        success: false,
                        duration: duration,
                        error: result.error || 'No questions generated'
                    });
                }

            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;

                console.log(`   ⚠️  ERROR: ${error.message} (${duration}ms)`);
                results.push({
                    model: model,
                    success: false,
                    duration: duration,
                    error: error.message
                });
            }

            // Small delay between tests
            await new Promise(resolve => setTimeout(resolve, 1000));
            console.log('');
        }

        // Generate summary
        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;
        const avgDuration = Math.round(
            results.reduce((sum, r) => sum + r.duration, 0) / results.length
        );

        console.log('📊 SIMULATION RESULTS:');
        console.log('═'.repeat(50));
        console.log(`Total Models Tested: ${totalCount}`);
        console.log(`Successful Models: ${successCount}`);
        console.log(`Failed Models: ${totalCount - successCount}`);
        console.log(`Average Response Time: ${avgDuration}ms`);
        console.log(`Success Rate: ${Math.round((successCount / totalCount) * 100)}%`);
        console.log('═'.repeat(50));

        if (successCount > 0) {
            console.log('\n✅ WORKING MODELS:');
            results.filter(r => r.success).forEach(r => {
                console.log(`   • ${r.model.name}: ${r.questionsGenerated} questions in ${r.duration}ms`);
            });
        }

        if (successCount < totalCount) {
            console.log('\n❌ FAILED MODELS:');
            results.filter(r => !r.success).forEach(r => {
                console.log(`   • ${r.model.name}: ${r.error}`);
            });
        }

        
    } catch (error) {
        console.error('❌ Failed to run simulation:', error);
    }
    
    // Exit after simulation
    setTimeout(() => {
        console.log('\n🏁 Simulation complete. Exiting...\n');
        app.quit();
    }, 2000);
}

app.whenReady().then(() => {
    createWindow();
    
    // Wait a bit for the app to initialize, then run simulation
    setTimeout(runSimulation, 3000);
});

app.on('window-all-closed', () => {
    app.quit();
});
