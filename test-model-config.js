const fs = require('fs');
const path = require('path');

console.log('🔧 Testing Model Configuration');
console.log('=' .repeat(50));

// Check custom models
const customModelsPath = path.join(__dirname, 'src', 'config', 'models.json');
console.log('\n📋 Custom Models:');
console.log(`   Path: ${customModelsPath}`);
console.log(`   Exists: ${fs.existsSync(customModelsPath)}`);

if (fs.existsSync(customModelsPath)) {
    try {
        const customModels = JSON.parse(fs.readFileSync(customModelsPath, 'utf8'));
        console.log(`   Count: ${customModels.length}`);
        
        customModels.forEach((model, index) => {
            console.log(`   ${index + 1}. ${model.name} (${model.id})`);
        });
    } catch (error) {
        console.log(`   Error: ${error.message}`);
    }
}

// Check removed models
const removedModelsPath = path.join(__dirname, 'src', 'config', 'removed-models.json');
console.log('\n📋 Removed Models:');
console.log(`   Path: ${removedModelsPath}`);
console.log(`   Exists: ${fs.existsSync(removedModelsPath)}`);

if (fs.existsSync(removedModelsPath)) {
    try {
        const removedModels = JSON.parse(fs.readFileSync(removedModelsPath, 'utf8'));
        console.log(`   Count: ${removedModels.length}`);
        
        // Check if deepseek is in removed list
        const deepseekRemoved = removedModels.includes('deepseek/deepseek-chat-v3-0324:free');
        console.log(`   DeepSeek in removed list: ${deepseekRemoved ? 'YES (PROBLEM!)' : 'NO (GOOD)'}`);
        
        if (deepseekRemoved) {
            console.log('   ❌ This is why your model is not showing up!');
        }
    } catch (error) {
        console.log(`   Error: ${error.message}`);
    }
}

// Simulate the IPC handler logic
console.log('\n🧪 Simulating Model Loading Logic:');

try {
    // Read custom models
    let customModels = [];
    if (fs.existsSync(customModelsPath)) {
        customModels = JSON.parse(fs.readFileSync(customModelsPath, 'utf8'));
    }
    
    console.log(`   Custom models found: ${customModels.length}`);
    
    if (customModels.length > 0) {
        console.log('   ✅ Using ONLY custom models (as intended)');
        console.log('   📋 Models that will appear in dropdown:');
        
        customModels.forEach((model, index) => {
            console.log(`      ${index + 1}. ${model.name} (${model.id})`);
        });
    } else {
        console.log('   ⚠️  No custom models, would fall back to default models');
        
        // Simulate default models filtering
        const defaultModels = [
            { id: 'deepseek/deepseek-chat-v3-0324:free', name: 'DeepSeek Chat V3 (Latest)', custom: false },
            { id: 'google/gemini-2.0-flash-exp:free', name: 'Gemini 2.0 Flash (Fast)', custom: false },
            { id: 'mistralai/devstral-small:free', name: 'Mistral Devstral Small (Code)', custom: false }
        ];
        
        let removedModels = [];
        if (fs.existsSync(removedModelsPath)) {
            removedModels = JSON.parse(fs.readFileSync(removedModelsPath, 'utf8'));
        }
        
        const availableDefaultModels = defaultModels.filter(model =>
            !removedModels.includes(model.id)
        );
        
        console.log('   📋 Available default models:');
        availableDefaultModels.forEach((model, index) => {
            console.log(`      ${index + 1}. ${model.name} (${model.id})`);
        });
    }
    
} catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
}

console.log('\n🎯 Expected Result:');
console.log('   Your dropdown should now show ONLY:');
console.log('   1. DeepSeek Chat V3 (Latest)');
console.log('');
console.log('🚀 Restart your application to see the changes:');
console.log('   npm start');
