const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

console.log('🧹 Cleaning Database of Hardcoded Models');
console.log('=' .repeat(60));

const dbPath = path.join(__dirname, 'data', 'bot.db');

if (!fs.existsSync(dbPath)) {
    console.log('❌ Database file not found');
    process.exit(1);
}

const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ Database connection error:', err.message);
        process.exit(1);
    }
    
    console.log('✅ Connected to database');
    
    // Clean model_stats table
    console.log('\n🧹 Cleaning model_stats table...');
    
    // First, check what's in there
    db.all('SELECT DISTINCT model FROM model_stats', [], (err, rows) => {
        if (err) {
            console.error('❌ Error checking model_stats:', err.message);
            db.close();
            return;
        }
        
        console.log(`   Found ${rows.length} distinct models:`);
        rows.forEach((row, index) => {
            console.log(`      ${index + 1}. ${row.model}`);
        });
        
        // Delete all hardcoded model entries
        const hardcodedPatterns = [
            'google/gemini%',
            'google/gemma%', 
            'nvidia/llama%',
            'deepseek/%',
            'qwen/%',
            'meta-llama/%',
            'mistralai/%'
        ];
        
        console.log('\n🗑️  Removing hardcoded model entries...');
        
        let deletedCount = 0;
        let patternsProcessed = 0;
        
        hardcodedPatterns.forEach(pattern => {
            db.run('DELETE FROM model_stats WHERE model LIKE ?', [pattern], function(err) {
                if (err) {
                    console.error(`❌ Error deleting ${pattern}:`, err.message);
                } else {
                    if (this.changes > 0) {
                        console.log(`   ✅ Deleted ${this.changes} entries matching "${pattern}"`);
                        deletedCount += this.changes;
                    }
                }
                
                patternsProcessed++;
                
                if (patternsProcessed === hardcodedPatterns.length) {
                    console.log(`\n✅ Total deleted: ${deletedCount} hardcoded model entries`);
                    
                    // Check what's left
                    db.all('SELECT DISTINCT model FROM model_stats', [], (err, remainingRows) => {
                        if (err) {
                            console.error('❌ Error checking remaining models:', err.message);
                        } else {
                            console.log(`\n📋 Remaining models: ${remainingRows.length}`);
                            remainingRows.forEach((row, index) => {
                                console.log(`      ${index + 1}. ${row.model}`);
                            });
                        }
                        
                        // Clean other potential model references
                        cleanOtherTables();
                    });
                }
            });
        });
    });
    
    function cleanOtherTables() {
        console.log('\n🧹 Checking other tables for model references...');
        
        // Check config table for model-related entries
        db.all("SELECT key, value FROM config WHERE key LIKE '%model%' OR value LIKE '%google/%' OR value LIKE '%deepseek/%'", [], (err, rows) => {
            if (err) {
                console.error('❌ Error checking config table:', err.message);
            } else {
                if (rows.length > 0) {
                    console.log(`\n📋 Config table model references: ${rows.length}`);
                    rows.forEach((row, index) => {
                        console.log(`      ${index + 1}. ${row.key}: ${row.value}`);
                    });
                    
                    // Ask if we should clean these too
                    console.log('\n⚠️  Found model references in config table');
                    console.log('   These might be legitimate settings, not cleaning automatically');
                } else {
                    console.log('   ✅ No model references found in config table');
                }
            }
            
            // Check cache table
            db.all("SELECT COUNT(*) as count FROM cache WHERE data LIKE '%google/%' OR data LIKE '%deepseek/%'", [], (err, rows) => {
                if (err) {
                    console.error('❌ Error checking cache table:', err.message);
                } else {
                    const count = rows[0].count;
                    if (count > 0) {
                        console.log(`\n📋 Cache table model references: ${count}`);
                        console.log('   Clearing model-related cache entries...');
                        
                        db.run("DELETE FROM cache WHERE data LIKE '%google/%' OR data LIKE '%deepseek/%' OR data LIKE '%nvidia/%' OR data LIKE '%qwen/%' OR data LIKE '%meta-llama/%' OR data LIKE '%mistralai/%'", function(err) {
                            if (err) {
                                console.error('❌ Error clearing cache:', err.message);
                            } else {
                                console.log(`   ✅ Cleared ${this.changes} cache entries with model references`);
                            }
                            
                            finishCleaning();
                        });
                    } else {
                        console.log('   ✅ No model references found in cache table');
                        finishCleaning();
                    }
                }
            });
        });
    }
    
    function finishCleaning() {
        console.log('\n🎯 Database Cleaning Complete!');
        console.log('✅ Removed all hardcoded model references');
        console.log('✅ Database is now clean of hardcoded models');
        console.log('✅ Only user-configured models will be used');
        
        db.close();
    }
});
